import React from "react";
import { motion } from "framer-motion";
import { Menu } from "antd";
import type { MenuProps } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import {
  History,
  Target,
  MessageSquare,
  Zap,
  Users,
  Clock,
} from "lucide-react";
import { ChevronRight } from "lucide-react";
import { useLanguage } from "../../contexts/LanguageContext";

interface HistoryItem {
  id: string;
  title: string;
  brand: string;
  budget: string;
  audience: string;
  industry: string;
  competitors: string;
  features: string;
  createdAt: string;
}

interface SidebarProps {
  onSelectHistory?: (historyItem: HistoryItem) => void;
  collapsed?: boolean;
  isMobile?: boolean;
}

type MenuItem = Required<MenuProps>["items"][number];

const Sidebar: React.FC<SidebarProps> = ({
  onSelectHistory,
  collapsed = false,
  isMobile = false,
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const historyItems = [
    {
      id: "1",
      title: "多多柠蒙茶50-100万广告策略",
      brand: "多多柠蒙茶",
      budget: "50-100万元",
      audience: "18-35岁都市白领女性",
      industry: "食品饮料 > 茶饮 > 柠檬茶",
      competitors: "喜茶、奈雪的茶、茶颜悦色",
      features: "真实柠檬片、0糖0脂、清香回甘、便携包装",
      createdAt: "2024-01-15 14:30",
    },
    {
      id: "2",
      title: "科沃斯洗拖一体机100万广告策略",
      brand: "科沃斯洗拖一体机",
      budget: "100万元",
      audience: "25-45岁中产家庭",
      industry: "家电 > 清洁电器 > 洗地机",
      competitors: "石头科技、云鲸、添可",
      features: "自清洁、智能识别污渍、超长续航、静音设计",
      createdAt: "2024-01-14 16:45",
    },
    {
      id: "3",
      title: "海龟爸爸防晒霜20万广告策略",
      brand: "海龟爸爸防晒霜",
      budget: "20万元",
      audience: "25-40岁宝妈群体",
      industry: "大美妆 > 护肤 > 防晒",
      competitors: "安耐晒、资生堂、理肤泉",
      features: "儿童可用、温和无刺激、防水防汗、SPF50+",
      createdAt: "2024-01-13 10:20",
    },
  ];

  // 创建历史记录子菜单项
  const historySubItems: MenuItem[] = historyItems.map((item, index) => ({
    key: `history-${item.id}`,
    icon: (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
      >
        <Clock className="w-4 h-4" />
      </motion.div>
    ),
    label: (
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: index * 0.1 }}
        whileHover={{
          scale: 1.02,
          x: 4,
          transition: { duration: 0.2 },
        }}
        className="text-xs"
      >
        <div className="font-medium truncate text-app-secondary">
          {item.title}
        </div>
        <div className="text-app-tertiary mt-1">
          {item.createdAt.split(" ")[0]}
        </div>
      </motion.div>
    ),
    onClick: () => {
      onSelectHistory?.(item);
      navigate(`/strategy/${item.id}`);
    },
    style: {
      padding: "8px 16px",
      height: "auto",
      lineHeight: 1.4,
    },
  }));

  // 创建主菜单项
  const menuItems: MenuItem[] = [
    {
      key: "history",
      icon: (
        <motion.div
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ duration: 0.2 }}
        >
          <History className="w-5 h-5" />
        </motion.div>
      ),
      label: (
        <motion.span whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          {t("nav.history")}
        </motion.span>
      ),
      children: historySubItems,
    },
    {
      key: "strategy",
      icon: (
        <motion.div
          whileHover={{ scale: 1.1, rotate: -5 }}
          transition={{ duration: 0.2 }}
        >
          <Target className="w-5 h-5" />
        </motion.div>
      ),
      label: (
        <motion.span whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          {t("nav.strategy")}
        </motion.span>
      ),
      onClick: () => navigate("/strategy"),
    },
    {
      key: "sentiment",
      icon: (
        <motion.div
          whileHover={{ scale: 1.1, y: -2 }}
          transition={{ duration: 0.2 }}
        >
          <MessageSquare className="w-5 h-5" />
        </motion.div>
      ),
      label: (
        <motion.span whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          {t("nav.sentiment")}
        </motion.span>
      ),
      onClick: () => navigate("/sentiment"),
    },
    {
      key: "content",
      icon: (
        <motion.div
          whileHover={{ scale: 1.1, rotate: 10 }}
          transition={{ duration: 0.2 }}
        >
          <Zap className="w-5 h-5" />
        </motion.div>
      ),
      label: (
        <motion.span whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          {t("nav.content")}
        </motion.span>
      ),
      onClick: () => navigate("/content"),
    },
    {
      key: "influencer",
      icon: (
        <motion.div
          whileHover={{ scale: 1.1, y: -2 }}
          transition={{ duration: 0.2 }}
        >
          <Users className="w-5 h-5" />
        </motion.div>
      ),
      label: (
        <motion.span whileHover={{ x: 2 }} transition={{ duration: 0.2 }}>
          {t("nav.influencer")}
        </motion.span>
      ),
      onClick: () => navigate("/influencer"),
    },
  ];

  // 根据当前路径获取选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path.includes('/strategy')) return ['strategy'];
    if (path.includes('/sentiment')) return ['sentiment'];
    if (path.includes('/content')) return ['content'];
    if (path.includes('/influencer')) return ['influencer'];
    return ['strategy']; // 默认选中策略页面
  };

  const handleMenuClick: MenuProps["onClick"] = (e) => {
    const key = e.key;
    if (key.startsWith("history-")) {
      // 历史记录项已在菜单项定义中处理
      return;
    }
    // 路由导航已在菜单项的 onClick 中处理
  };

  return (
    <div className="h-full flex flex-col bg-dark-900">
      {/* 菜单区域 */}
      <div className="flex-1 p-4">
        <Menu
          mode="inline"
          theme="dark"
          selectedKeys={getSelectedKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          className="bg-transparent border-none custom-menu"
          inlineCollapsed={collapsed}
          style={{
            backgroundColor: "transparent",
            border: "none",
          }}
          expandIcon={({ isOpen }) => (
            <motion.div
              animate={{ rotate: isOpen ? 90 : 0 }}
              transition={{ duration: 0.3 }}
              className="inline-flex"
            >
              <span aria-label="Expand submenu">
                <ChevronRight className="w-4 h-4" aria-hidden="true" />
              </span>
            </motion.div>
          )}
        />
      </div>
    </div>
  );
};

export default Sidebar;
