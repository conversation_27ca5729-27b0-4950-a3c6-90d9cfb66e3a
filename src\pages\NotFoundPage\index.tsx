import React from "react";
import { motion } from "framer-motion";
import { Home, ArrowLeft } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useLanguage } from "../../contexts/LanguageContext";

const NotFoundPage: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  return (
    <div className="h-full flex items-center justify-center p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center max-w-md mx-auto"
      >
        <div className="mb-8">
          <div className="text-8xl font-bold text-gray-600 mb-4">404</div>
          <h1 className="text-2xl font-bold text-white mb-2">页面未找到</h1>
          <p className="text-gray-400">{t("notFound.description")}</p>
        </div>

        <div className="space-y-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => navigate(-1)}
            className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gray-800/50 hover:bg-gray-700/50 text-white rounded-lg transition-all duration-300 border border-gray-700/50 hover:border-gray-600/50"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回上一页</span>
          </motion.button>

          <Link to="/">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 hover:from-cyan-400 hover:via-blue-400 hover:to-purple-500 text-white rounded-lg transition-all duration-300 shadow-lg shadow-blue-500/25"
            >
              <Home className="w-4 h-4" />
              <span>回到首页</span>
            </motion.button>
          </Link>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>如果您认为这是一个错误，请联系我们的技术支持</p>
        </div>
      </motion.div>
    </div>
  );
};

export default NotFoundPage;
