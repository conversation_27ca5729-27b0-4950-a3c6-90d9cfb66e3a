/**
 * CSS 变量定义
 * 将 TypeScript 颜色对象转换为 CSS 变量，便于在样式文件中使用
 */

:root {
  /* 调色板 - 蓝色系 */
  --color-blue-50: #eff6ff;
  --color-blue-100: #dbeafe;
  --color-blue-200: #bfdbfe;
  --color-blue-300: #93c5fd;
  --color-blue-400: #60a5fa;
  --color-blue-500: #3b82f6;
  --color-blue-600: #2563eb;
  --color-blue-700: #1d4ed8;
  --color-blue-800: #1e40af;
  --color-blue-900: #1e3a8a;

  /* 调色板 - 青色系 */
  --color-cyan-50: #ecfeff;
  --color-cyan-100: #cffafe;
  --color-cyan-200: #a5f3fc;
  --color-cyan-300: #67e8f9;
  --color-cyan-400: #22d3ee;
  --color-cyan-500: #06b6d4;
  --color-cyan-600: #0891b2;
  --color-cyan-700: #0e7490;
  --color-cyan-800: #155e75;
  --color-cyan-900: #164e63;

  /* 调色板 - 紫色系 */
  --color-purple-50: #faf5ff;
  --color-purple-100: #f3e8ff;
  --color-purple-200: #e9d5ff;
  --color-purple-300: #d8b4fe;
  --color-purple-400: #c084fc;
  --color-purple-500: #a855f7;
  --color-purple-600: #9333ea;
  --color-purple-700: #7c3aed;
  --color-purple-800: #6b21a8;
  --color-purple-900: #581c87;

  /* 调色板 - 绿色系 */
  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-200: #bbf7d0;
  --color-green-300: #86efac;
  --color-green-400: #4ade80;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;
  --color-green-800: #166534;
  --color-green-900: #14532d;

  /* 调色板 - 琥珀色系 */
  --color-amber-50: #fffbeb;
  --color-amber-100: #fef3c7;
  --color-amber-200: #fde68a;
  --color-amber-300: #fcd34d;
  --color-amber-400: #fbbf24;
  --color-amber-500: #f59e0b;
  --color-amber-600: #d97706;
  --color-amber-700: #b45309;
  --color-amber-800: #92400e;
  --color-amber-900: #78350f;

  /* 调色板 - 红色系 */
  --color-red-50: #fef2f2;
  --color-red-100: #fee2e2;
  --color-red-200: #fecaca;
  --color-red-300: #fca5a5;
  --color-red-400: #f87171;
  --color-red-500: #ef4444;
  --color-red-600: #dc2626;
  --color-red-700: #b91c1c;
  --color-red-800: #991b1b;
  --color-red-900: #7f1d1d;

  /* 调色板 - 灰色系 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* 调色板 - 石板色系 */
  --color-slate-50: #f8fafc;
  --color-slate-100: #f1f5f9;
  --color-slate-200: #e2e8f0;
  --color-slate-300: #cbd5e1;
  --color-slate-400: #94a3b8;
  --color-slate-500: #64748b;
  --color-slate-600: #475569;
  --color-slate-700: #334155;
  --color-slate-800: #1e293b;
  --color-slate-900: #0f172a;

  /* 主题颜色 */
  --color-primary: var(--color-blue-500);
  --color-primary-light: var(--color-blue-400);
  --color-primary-dark: var(--color-blue-600);
  --color-primary-hover: var(--color-blue-400);
  --color-primary-active: var(--color-blue-600);

  --color-secondary: var(--color-cyan-500);
  --color-secondary-light: var(--color-cyan-400);
  --color-secondary-dark: var(--color-cyan-600);
  --color-secondary-hover: var(--color-cyan-400);
  --color-secondary-active: var(--color-cyan-600);

  --color-accent: var(--color-purple-600);
  --color-accent-light: var(--color-purple-500);
  --color-accent-dark: var(--color-purple-700);
  --color-accent-hover: var(--color-purple-500);
  --color-accent-active: var(--color-purple-700);

  /* 功能色 */
  --color-success: #10b981;
  --color-success-light: var(--color-green-400);
  --color-success-dark: var(--color-green-600);
  --color-success-bg: rgba(16, 185, 129, 0.1);
  --color-success-border: rgba(16, 185, 129, 0.3);

  --color-warning: var(--color-amber-500);
  --color-warning-light: var(--color-amber-400);
  --color-warning-dark: var(--color-amber-600);
  --color-warning-bg: rgba(245, 158, 11, 0.1);
  --color-warning-border: rgba(245, 158, 11, 0.3);

  --color-error: var(--color-red-500);
  --color-error-light: var(--color-red-400);
  --color-error-dark: var(--color-red-600);
  --color-error-bg: rgba(239, 68, 68, 0.1);
  --color-error-border: rgba(239, 68, 68, 0.3);

  --color-info: #06b6d4;
  --color-info-light: var(--color-cyan-400);
  --color-info-dark: var(--color-cyan-600);
  --color-info-bg: rgba(6, 182, 212, 0.1);
  --color-info-border: rgba(6, 182, 212, 0.3);

  /* 背景色 */
  --bg-base: var(--color-slate-900);
  --bg-container: var(--color-slate-800);
  --bg-elevated: var(--color-slate-700);
  --bg-header: rgba(17, 24, 39, 0.8);
  --bg-sidebar: rgba(17, 24, 39, 0.3);
  --bg-modal: rgba(17, 24, 39, 0.95);
  --bg-dropdown: rgba(31, 41, 55, 0.95);
  --bg-input: rgba(55, 65, 81, 0.5);
  --bg-trigger: rgba(55, 65, 81, 0.5);

  /* 文字颜色 */
  --text-primary: var(--color-slate-100);
  --text-secondary: var(--color-slate-400);
  --text-tertiary: var(--color-slate-500);
  --text-quaternary: var(--color-slate-600);
  --text-placeholder: var(--color-gray-400);
  --text-white: #ffffff;
  --text-disabled: var(--color-slate-600);

  /* 边框颜色 */
  --border-primary: var(--color-gray-700);
  --border-secondary: var(--color-gray-600);
  --border-light: rgba(75, 85, 99, 0.5);
  --border-split: var(--color-gray-700);
  --border-input: rgba(75, 85, 99, 0.5);
  --border-input-hover: var(--color-blue-400);
  --border-input-active: var(--color-blue-500);

  /* 状态颜色 */
  --state-hover: rgba(55, 65, 81, 0.5);
  --state-active: rgba(75, 85, 99, 0.7);
  --state-focus: var(--color-blue-500);
  --state-disabled: rgba(107, 114, 128, 0.5);
  --state-loading: var(--color-blue-500);

  /* 渐变色 */
  --gradient-primary: linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6);
  --gradient-primary-hover: linear-gradient(90deg, #0891b2, #2563eb, #7c3aed);
  --gradient-button: linear-gradient(90deg, #06b6d4, #3b82f6, #9333ea);
  --gradient-button-hover: linear-gradient(90deg, #22d3ee, #60a5fa, #a855f7);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-blue: 0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(147, 51, 234, 0.3);
  --shadow-blue-hover: 0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4);
  --shadow-menu: 0 4px 15px rgba(59, 130, 246, 0.25);
  --shadow-menu-hover: 0 0 15px rgba(59, 130, 246, 0.3);

  /* 菜单颜色 */
  --menu-dark-bg: transparent;
  --menu-dark-item-bg: transparent;
  --menu-dark-item-selected-bg: transparent;
  --menu-dark-item-hover-bg: rgba(55, 65, 81, 0.5);
  --menu-dark-submenu-item-bg: rgba(31, 41, 55, 0.5);
  --menu-dark-item-color: var(--color-slate-400);
  --menu-dark-item-selected-color: #ffffff;
  --menu-dark-item-hover-color: var(--color-slate-100);
  --menu-dark-selected-gradient: var(--gradient-primary);
  --menu-dark-selected-gradient-hover: var(--gradient-primary-hover);

  /* 滚动条颜色 */
  --scrollbar-track: rgba(31, 41, 55, 0.3);
  --scrollbar-thumb: rgba(75, 85, 99, 0.5);
  --scrollbar-thumb-hover: rgba(107, 114, 128, 0.7);
}
