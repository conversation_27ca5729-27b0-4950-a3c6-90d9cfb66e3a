import { createServiceInstance, createMockServiceInstance } from './serviceUtils';

// 定义示例服务的数据类型
export interface DemoService {
  fetchDemoData: () => Promise<{
    success: boolean;
    data: Array<{
      id: string;
      name: string;
      description: string;
    }>;
  }>;
  
  generateDemo: (params: { type: string; name: string }) => Promise<{
    success: boolean;
    data: {
      id: string;
      result: string;
      createdAt: string;
    };
  }>;
}

// 创建正常的API服务实例
export const demoService = {
  // 获取示例数据
  fetchDemoData: () => {
    const apiInstance = createServiceInstance({
      baseURL: '/api/v1', // 可以指定不同的基础URL
    });
    
    return apiInstance.get('/demo/data');
  },
  
  // 生成示例内容
  generateDemo: (params: { type: string; name: string }) => {
    const apiInstance = createServiceInstance();
    return apiInstance.post('/demo/generate', params);
  },
  
  // 获取策略示例
  getStrategyExample: () => {
    const apiInstance = createServiceInstance();
    return apiInstance.get<StrategyResult>('/demo/strategy-example');
  }
};

// 创建模拟数据服务实例（用于开发/测试）
export const mockDemoService = createMockServiceInstance({
  // 模拟获取示例数据
  fetchDemoData: {
    success: true,
    data: [
      { 
        id: '1', 
        name: '示例1', 
        description: '这是第一个示例' 
      },
      { 
        id: '2', 
        name: '示例2', 
        description: '这是第二个示例' 
      }
    ]
  },
  
  // 模拟生成示例内容
  generateDemo: {
    success: true,
    data: {
      id: '123',
      result: '这是一个生成的示例结果',
      createdAt: new Date().toISOString()
    }
  }
});
