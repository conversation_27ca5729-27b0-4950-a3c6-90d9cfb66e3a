/**
 * 环境变量和权限相关的工具函数
 */

/**
 * 获取当前应用环境
 * @returns 当前环境: 'development' | 'test' | 'production'
 */
export const getAppEnvironment = (): 'development' | 'test' | 'production' => {
  return (import.meta.env.VITE_ENV as 'development' | 'test' | 'production') || 'development';
};

/**
 * 检查当前是否为开发环境
 * @returns 布尔值，表示是否是开发环境
 */
export const isLocalEnv = (): boolean => {
  const env = getAppEnvironment();
  return env === 'development';
};

/**
 * 检查当前是否为测试环境
 * @returns 布尔值，表示是否是测试环境
 */
export const isTestEnv = (): boolean => {
  return getAppEnvironment() === 'test';
};

/**
 * 检查当前是否为生产环境
 * @returns 布尔值，表示是否是生产环境
 */
export const isProdEnv = (): boolean => {
  return getAppEnvironment() === 'production';
};

/**
 * 获取登录基础URL
 * @returns 登录基础URL
 */
export const getLoginBaseUrl = (): string => {
  return import.meta.env.VITE_LOGIN_BASE_URL || '';
};

/**
 * 获取API基础URL
 * @returns API基础URL
 */
export const getApiBaseUrl = (): string => {
  return import.meta.env.VITE_API_BASE_URL || '';
};

// 以下是用户角色相关的函数

/**
 * 获取当前环境的用户角色
 * @returns 当前用户角色：'admin' | 'user' | 'guest'
 */
export const getUserRole = (): 'admin' | 'user' | 'guest' => {
  return (import.meta.env.VITE_USER_ROLE as 'admin' | 'user' | 'guest') || 'guest';
};

/**
 * 检查当前用户是否是管理员
 * @returns 布尔值，表示是否是管理员
 */
export const isAdmin = (): boolean => {
  return getUserRole() === 'admin';
};

/**
 * 检查当前用户是否是普通用户或更高权限
 * @returns 布尔值，表示是否是普通用户或更高权限
 */
export const isUser = (): boolean => {
  const role = getUserRole();
  return role === 'user' || role === 'admin';
};

/**
 * 检查当前用户是否是访客
 * @returns 布尔值，表示是否是访客
 */
export const isGuest = (): boolean => {
  return getUserRole() === 'guest';
};

/**
 * 检查是否启用管理员功能
 * @returns 布尔值，表示是否启用管理员功能
 */
export const isAdminFeaturesEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_ADMIN_FEATURES === 'true';
};

/**
 * 检查是否启用高级分析功能
 * @returns 布尔值，表示是否启用高级分析功能
 */
export const isAdvancedAnalyticsEnabled = (): boolean => {
  return import.meta.env.VITE_ENABLE_ADVANCED_ANALYTICS === 'true';
};

/**
 * 获取应用标题
 * @returns 应用标题字符串
 */
export const getAppTitle = (): string => {
  return import.meta.env.VITE_APP_TITLE as string;
};

/**
 * 获取应用版本
 * @returns 应用版本字符串
 */
export const getAppVersion = (): string => {
  return import.meta.env.VITE_APP_VERSION as string;
};

export default {
  getAppEnvironment,
  isLocalEnv,
  isTestEnv,
  isProdEnv,
  getLoginBaseUrl,
  getApiBaseUrl,
  getUserRole,
  isAdmin,
  isUser,
  isGuest,
  isAdminFeaturesEnabled,
  isAdvancedAnalyticsEnabled,
  getAppTitle,
  getAppVersion,
};
