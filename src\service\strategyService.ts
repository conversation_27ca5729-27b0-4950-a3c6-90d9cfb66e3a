import { api } from "../utils/request";

// 策略生成参数
export interface StrategyParams {
  brandName: string;
  brandDescription: string;
  productName: string;
  productDescription: string;
  categoryId: number;
  platform: string;
  name: string;
}


// 策略历史记录
export interface StrategyHistory {
  id: string;
  brand: string;
  createdAt: string;
}

export interface IndustryCategoryOption {
  id: number;
  name: string;
  parentId: number;
  level: number;
  status: boolean;
}

// 策略相关API服务
export const strategyService = {
  // 生成营销策略,提交任务
  generateStrategy: (params: StrategyParams) => {
    return api.post<{
      taskId: string;
    }>("/strategy/strategy/createTask", params, {
      showLoading: true,
      showSuccess: true,
    });
  },

  // 获取策略详情
  getStrategyDetail: (id: string) => {
    return api.get<string>(`/strategy/${id}`);
  },

  // 获取策略历史记录
  getStrategyHistory: (page = 1, limit = 10) => {
    return api.get<{
      total: number;
      items: StrategyHistory[];
    }>("/strategy/history", {
      params: { page, limit },
    });
  },

  // 删除策略记录
  deleteStrategy: (id: string) => {
    return api.delete(`/strategy/${id}`, {
      showSuccess: true,
    });
  },


  // 获取行业分类
  getIndustryCategories: () => {
    return api.get<IndustryCategoryOption[]>("/strategy/strategy/categoryOptions", {
      showLoading: true,
    });
  },
};
