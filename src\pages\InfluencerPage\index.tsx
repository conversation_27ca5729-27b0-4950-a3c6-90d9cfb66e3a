import React from 'react';
import { motion } from 'framer-motion';
import { Users, Star, TrendingUp, Target } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const InfluencerPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="h-full p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mb-4 mx-auto">
            <Users className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">达人推荐</h1>
          <p className="text-gray-400">{t('influencer.aiDrivenSelectionSystem')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <Star className="w-8 h-8 text-yellow-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">达人评级</h3>
            <p className="text-gray-400 text-sm">基于多维度数据分析达人质量和影响力</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <TrendingUp className="w-8 h-8 text-green-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">数据分析</h3>
            <p className="text-gray-400 text-sm">分析达人粉丝画像、互动率等关键指标</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <Target className="w-8 h-8 text-red-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">精准匹配</h3>
            <p className="text-gray-400 text-sm">根据品牌需求智能匹配最适合的达人</p>
          </motion.div>
        </div>

        <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center">
          <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🚧</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-400 mb-2">功能开发中</h3>
          <p className="text-gray-500 mb-4">该功能正在开发中，敬请期待</p>
          <div className="text-sm text-gray-600">
            <p>即将支持：</p>
            <ul className="mt-2 space-y-1">
              <li>• 多平台达人数据库</li>
              <li>• 智能筛选算法</li>
              <li>• 合作效果预测</li>
              <li>• 达人管理系统</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default InfluencerPage;
