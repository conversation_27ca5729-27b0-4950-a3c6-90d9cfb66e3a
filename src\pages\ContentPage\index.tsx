import React from 'react';
import { motion } from 'framer-motion';
import { Zap, Smartphone, Camera, Edit3 } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const ContentPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="h-full p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mb-4 mx-auto">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">爆文生成</h1>
          <p className="text-gray-400">{t('AI驱动的社交媒体内容创作工具')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <Smartphone className="w-8 h-8 text-pink-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">小红书种草文</h3>
            <p className="text-gray-400 text-sm">生成符合小红书调性的种草内容，提升转化率</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <Camera className="w-8 h-8 text-red-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">抖音短视频脚本</h3>
            <p className="text-gray-400 text-sm">创作吸引眼球的短视频脚本和文案</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <Edit3 className="w-8 h-8 text-blue-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">微博热点文案</h3>
            <p className="text-gray-400 text-sm">结合热点话题，生成高传播性微博内容</p>
          </motion.div>
        </div>

        <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center">
          <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🚧</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-400 mb-2">功能开发中</h3>
          <p className="text-gray-500 mb-4">该功能正在开发中，敬请期待</p>
          <div className="text-sm text-gray-600">
            <p>即将支持：</p>
            <ul className="mt-2 space-y-1">
              <li>• 多平台内容模板</li>
              <li>• AI智能改写</li>
              <li>• 热点话题结合</li>
              <li>• 内容效果预测</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ContentPage;
