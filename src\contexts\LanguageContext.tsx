import React, { createContext, useContext, useState } from 'react';

interface LanguageContextType {
  language: 'zh' | 'en';
  setLanguage: (lang: 'zh' | 'en') => void;
  t: (key: string) => string;
}

const translations = {
  zh: {
    'nav.history': '生成历史',
    'nav.strategy': 'AI广告投放策略',
    'nav.sentiment': '评论情感分析',
    'nav.content': '爆文生成',
    'nav.influencer': '达人推荐',
    'form.brand': '品牌名称',
    'form.brandDescription': '品牌描述',
    'form.productName': '产品名称',
    'form.audience': '人群定位',
    'form.competitors': '竞品分析',
    'form.industry': '所属行业',
    'form.budget': '投放预算',
    'form.features': '产品卖点',
    'form.generate': '生成策略',
    'form.generated': '重新生成策略',
    'demo.title': '广告策略生成演示',
    'demo.subtitle': '点击下方卡片体验AI策略生成',
    'demo.generate': '点击生成策略',
    'results.core': '核心策略',
    'results.content': '详细种草方案',
    'results.influencer': '达人策略筛选方案',
    'button.viewContent': '查看详细种草方案',
    'button.viewInfluencer': '查看达人策略筛选方案',
    'button.newStrategy': '生成新策略'
  },
  en: {
    'nav.history': 'History',
    'nav.strategy': 'AI Ad Strategy',
    'nav.sentiment': 'Sentiment Analysis',
    'nav.content': 'Content Generator',
    'nav.influencer': 'Influencer Recommendation',
    'form.brand': 'Brand Name',
    'form.brandDescription': 'Brand Description',
    'form.productName': 'Product Name',
    'form.audience': 'Target Audience',
    'form.competitors': 'Competitors',
    'form.industry': 'Industry',
    'form.budget': 'Budget',
    'form.features': 'Product Features',
    'form.generate': 'Generate Strategy',
    'form.generated': 'Regenerate Strategy',
    'demo.title': 'Ad Strategy Demo',
    'demo.subtitle': 'Click cards below to experience AI strategy generation',
    'demo.generate': 'Generate Strategy',
    'results.core': 'Core Strategy',
    'results.content': 'Content Strategy',
    'results.influencer': 'Influencer Strategy',
    'button.viewContent': 'View Content Strategy',
    'button.viewInfluencer': 'View Influencer Strategy',
    'button.newStrategy': 'Generate New Strategy'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<'zh' | 'en'>('zh');

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations.zh] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};