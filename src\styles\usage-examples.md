# 颜色对象使用指南

本文档展示如何在项目中使用统一的颜色对象。颜色对象现已与 Tailwind CSS 配置完全一致。

> **注意**: 推荐优先使用 Tailwind CSS 类名（如 `bg-primary`），颜色对象主要用于需要动态计算或特殊场景。

## 1. 在 TypeScript/React 组件中使用

### 导入颜色对象
```typescript
import { colors, palette, tailwindColors, colorUtils } from '../styles/colors';
```

### 与 Tailwind CSS 的对应关系
```typescript
// 颜色对象与 Tailwind 类名对应
colors.primary.main        // 对应 bg-primary, text-primary
colors.background.container // 对应 bg-app-container
colors.text.secondary      // 对应 text-app-secondary
colors.border.light        // 对应 border-app-light
```

### 基本使用
```typescript
// 使用主题色
const buttonStyle = {
  backgroundColor: colors.primary.main,
  color: colors.text.white,
  border: `1px solid ${colors.border.primary}`,
};

// 使用功能色
const successMessage = {
  backgroundColor: colors.success.bg,
  color: colors.success.main,
  border: `1px solid ${colors.success.border}`,
};

// 使用调色板
const customStyle = {
  backgroundColor: palette.blue[500],
  color: palette.slate[100],
};
```

### 在 styled-components 中使用
```typescript
import styled from 'styled-components';
import { colors } from '../styles/colors';

const StyledButton = styled.button`
  background: ${colors.gradients.primary};
  color: ${colors.text.white};
  border: 1px solid ${colors.border.primary};
  box-shadow: ${colors.shadow.base};
  
  &:hover {
    background: ${colors.gradients.primaryHover};
    box-shadow: ${colors.shadow.blue};
  }
`;
```

### 使用工具函数
```typescript
import { colorUtils } from '../styles/colors';

// 创建半透明颜色
const overlayStyle = {
  backgroundColor: colorUtils.rgba(colors.background.base, 0.8),
};

// 获取渐变
const gradientStyle = {
  background: colorUtils.getGradient('primary'),
};

// 获取阴影
const cardStyle = {
  boxShadow: colorUtils.getShadow('md'),
};
```

## 2. 在 CSS 文件中使用

### 使用 CSS 变量
```css
.custom-button {
  background: var(--color-primary);
  color: var(--text-white);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-base);
}

.custom-button:hover {
  background: var(--color-primary-hover);
  box-shadow: var(--shadow-blue);
}

.success-message {
  background: var(--color-success-bg);
  color: var(--color-success);
  border: 1px solid var(--color-success-border);
}
```

### 使用渐变
```css
.gradient-background {
  background: var(--gradient-primary);
}

.gradient-text {
  background: var(--gradient-button);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

## 3. 在 Tailwind CSS 中使用

### 扩展 Tailwind 配置
在 `tailwind.config.js` 中添加自定义颜色：

```javascript
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          // ... 其他色阶
          500: '#3b82f6',
          // ... 其他色阶
          900: '#1e3a8a',
        },
        // 添加其他颜色
      },
    },
  },
};
```

### 使用自定义类名
```html
<div class="bg-primary-500 text-white border border-gray-700">
  主要按钮
</div>

<div class="bg-success-bg text-success border border-success-border">
  成功消息
</div>
```

## 4. 在 Ant Design 主题中使用

```typescript
import { ConfigProvider } from 'antd';
import { colors } from '../styles/colors';

const theme = {
  token: {
    colorPrimary: colors.primary.main,
    colorSuccess: colors.success.main,
    colorWarning: colors.warning.main,
    colorError: colors.error.main,
    colorInfo: colors.info.main,
    colorBgBase: colors.background.base,
    colorBgContainer: colors.background.container,
    colorText: colors.text.primary,
    colorTextSecondary: colors.text.secondary,
    colorBorder: colors.border.primary,
    boxShadow: colors.shadow.base,
  },
  components: {
    Button: {
      colorPrimary: colors.primary.main,
      colorPrimaryHover: colors.primary.hover,
      colorPrimaryActive: colors.primary.active,
    },
    Input: {
      colorBgContainer: colors.background.input,
      colorBorder: colors.border.input,
      activeBorderColor: colors.border.inputActive,
      hoverBorderColor: colors.border.inputHover,
    },
  },
};
```

## 5. 颜色分类说明

### 主题色 (colors.primary, colors.secondary, colors.accent)
- 用于品牌色、主要按钮、链接等
- 包含 main, light, dark, hover, active 状态

### 功能色 (colors.success, colors.warning, colors.error, colors.info)
- 用于状态提示、消息通知等
- 包含主色、背景色、边框色

### 背景色 (colors.background)
- 用于各种背景层级
- 包含基础背景、容器背景、悬浮背景等

### 文字色 (colors.text)
- 用于不同层级的文字
- 包含主要文字、次要文字、占位符等

### 边框色 (colors.border)
- 用于边框、分割线等
- 包含主边框、次要边框、输入框边框等

### 渐变色 (colors.gradients)
- 预定义的渐变组合
- 用于按钮、背景、装饰等

### 阴影 (colors.shadow)
- 预定义的阴影效果
- 用于卡片、按钮、悬浮元素等

## 6. 最佳实践

1. **优先使用主题色**：使用 `colors.primary` 而不是直接使用 `palette.blue[500]`
2. **保持一致性**：同类型的元素使用相同的颜色变量
3. **语义化命名**：使用 `colors.success.main` 而不是 `colors.green[500]`
4. **响应式设计**：考虑在不同主题下的颜色表现
5. **可访问性**：确保颜色对比度符合无障碍标准

## 7. 扩展颜色

如需添加新颜色，请在 `colors.ts` 文件中添加，并同步更新 `css-variables.css` 文件。

```typescript
// 在 colors.ts 中添加
export const colors = {
  // ... 现有颜色
  custom: {
    main: '#your-color',
    light: '#your-light-color',
    dark: '#your-dark-color',
  },
};
```

```css
/* 在 css-variables.css 中添加 */
:root {
  /* ... 现有变量 */
  --color-custom: #your-color;
  --color-custom-light: #your-light-color;
  --color-custom-dark: #your-dark-color;
}
```
