import React from 'react';
import { motion } from 'framer-motion';
import { MessageSquare, TrendingUp, BarChart3, FileText } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

const SentimentPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="h-full p-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-4xl mx-auto"
      >
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mb-4 mx-auto">
            <MessageSquare className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">{t('sentiment.title')}</h1>
          <p className="text-gray-400">{t('sentiment.subtitle')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <TrendingUp className="w-8 h-8 text-green-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">{t('sentiment.trendAnalysis')}</h3>
            <p className="text-gray-400 text-sm">{t('sentiment.trendDescription')}</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <BarChart3 className="w-8 h-8 text-blue-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">{t('sentiment.distribution')}</h3>
            <p className="text-gray-400 text-sm">{t('sentiment.distributionDescription')}</p>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.02 }}
            className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700/50"
          >
            <FileText className="w-8 h-8 text-purple-400 mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">{t('sentiment.keywordExtraction')}</h3>
            <p className="text-gray-400 text-sm">{t('sentiment.keywordDescription')}</p>
          </motion.div>
        </div>

        <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl p-8 border border-gray-700/50 text-center">
          <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4 mx-auto">
            <span className="text-2xl">🚧</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-400 mb-2">{t('sentiment.inDevelopment')}</h3>
          <p className="text-gray-500 mb-4">{t('sentiment.inDevelopmentDesc')}</p>
          <div className="text-sm text-gray-600">
            <p>{t('sentiment.comingSoon')}</p>
            <ul className="mt-2 space-y-1">
              <li>• {t('sentiment.batchUpload')}</li>
              <li>• {t('sentiment.realtimeMonitoring')}</li>
              <li>• {t('sentiment.multiPlatform')}</li>
              <li>• {t('sentiment.smartReport')}</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default SentimentPage;
