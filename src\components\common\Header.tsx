import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Brain, Globe, User, Bell, CheckCircle } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import LoginModal from '../auth/LoginModal';
import RegisterModal from '../auth/RegisterModal';
import useLoginStore from '@/contexts/LoginContext';

const Header: React.FC = () => {
  const { language, setLanguage } = useLanguage();
  const { openLoginModal:showLoginModal, toLogin: setShowLoginModal,close } = useLoginStore();
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);



  const handleLoginSuccess = () => {
    setIsLoggedIn(true);
  };

  const handleRegisterSuccess = () => {
    setIsLoggedIn(true);
  };

  const handleSwitchToRegister = () => {
    setShowLoginModal(false);
    setShowRegisterModal(true);
  };

  const handleSwitchToLogin = () => {
    setShowRegisterModal(false);
    setShowLoginModal(true);
  };

  return (
    <>
      <header className="bg-dark-900 backdrop-blur-sm border-b border-gray-800/50 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div 
              className="flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
            >
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg shadow-blue-500/25">
                <Brain className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">多麦AI</h1>
              </div>
            </motion.div>
          </div>

          <div className="flex items-center space-x-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setLanguage(language === 'zh' ? 'en' : 'zh')}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg transition-all duration-300 border border-gray-700/50 hover:border-gray-600/50"
            >
              <Globe className="w-4 h-4 text-gray-300" />
              <span className="text-sm text-gray-300 font-medium">
                {language === 'zh' ? '中文' : 'English'}
              </span>
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg transition-all duration-300 relative border border-gray-700/50 hover:border-gray-600/50"
            >
              <Bell className="w-5 h-5 text-gray-300" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-pink-500 to-red-500 rounded-full shadow-lg shadow-red-500/50"></div>
            </motion.button>

            {isLoggedIn ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-lg border border-green-500/30"
              >
                <CheckCircle className="w-4 h-4 text-green-400" />
                <span className="text-sm text-green-400 font-medium">已登录</span>
              </motion.div>
            ) : (
              <motion.button
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: "0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(147, 51, 234, 0.3)"
                }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setShowLoginModal(true)}
                className="relative flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 rounded-lg transition-all duration-300 overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <User className="w-4 h-4 text-white relative z-10" />
                <span className="text-sm text-white font-medium relative z-10">登录</span>
              </motion.button>
            )}
          </div>
        </div>
      </header>

      <LoginModal 
        isOpen={showLoginModal}
        onClose={close}
        onLoginSuccess={handleLoginSuccess}
        onSwitchToRegister={handleSwitchToRegister}
      />

      <RegisterModal 
        isOpen={showRegisterModal}
        onClose={close}
        onRegisterSuccess={handleRegisterSuccess}
        onSwitchToLogin={handleSwitchToLogin}
      />
    </>
  );
};

export default Header;