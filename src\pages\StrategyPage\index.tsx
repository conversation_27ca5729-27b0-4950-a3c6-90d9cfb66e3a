import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  StrategyGenerator,
  DemoCards,
  StrategyResults,
  ProgressDisplay,
} from "./components";
import {
  useShowResults,
  useShowProgress,
} from "@/contexts/ProgressContext";

const StrategyPage: React.FC = () => {
  // 使用 Zustand store 中的状态和方法
  const showResults = useShowResults();
  const showProgress = useShowProgress();


  return (
    <div className="flex h-full w-full">
      {/* 左侧表单区域 */}
      {!showProgress && (
        <div className="w-96 p-6 border-r border-gray-800/50">
          <AnimatePresence mode="wait">
            <motion.div
              key="strategy"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <StrategyGenerator />
            </motion.div>
          </AnimatePresence>
        </div>
      )}

      {/* 右侧主要内容区域 */}
      <div className="flex-1 p-6">
        <AnimatePresence mode="wait">
          {showProgress ? (
            <ProgressDisplay />
          ) : showResults ? (
            <StrategyResults />
          ) : (
            <DemoCards />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default StrategyPage;
