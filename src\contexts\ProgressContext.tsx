import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { devtools } from "zustand/middleware";

// 定义表单数据类型
export interface FormData {
  brand: string;
  audience: string;
  competitors: string;
  industry: string;
  budget: string;
  features: string;
  [key: string]: string; // 允许其他字符串字段
}

// 定义策略结果数据类型
export interface StrategyData {
  [key: string]: string; // 允许其他字符串字段
}

// 定义进度状态的类型
export interface ProgressState {
  taskId: string | null; // 任务ID，用于跟踪进度

  // 状态
  currentStep: number;
  completedSteps: number[];
  generatedContent: string;
  isTyping: boolean;
  progressPercentage: number;
  targetProgress: number;
  currentText: string;

  // 表单数据
  formData: FormData | null;
  showProgress: boolean;
  showResults: boolean;
  strategyData: Array<StrategyData> | null;

  // 方法
  updateCurrentStep: (step: number) => void;
  updateCompletedSteps: (steps: number[]) => void;
  updateGeneratedContent: (content: string) => void;
  updateIsTyping: (isTyping: boolean) => void;
  updateProgressPercentage: (percentage: number) => void;
  setProgressTarget: (target: number) => void;
  setCurrentText: (text: string) => void;

  // 表单和流程相关方法
  setFormData: (data: FormData | null) => void;
  setShowProgress: (show: boolean) => void;
  setShowResults: (show: boolean) => void;
  setStrategyData: (data: Array<StrategyData> | null) => void;
  startProgress: (formData: FormData, taskId: string) => void;
  completeProgress: (strategyData: Array<StrategyData>) => void;
  resetState: () => void;

  // 辅助方法
  addCompletedStep: (step: number) => void;
  resetProgress: () => void;
}

// 创建全局状态，使用中间件增强功能
export const useProgressStore = create<ProgressState>()(
  devtools(
    persist(
      (set) => ({
        // 状态初始值
        currentStep: 0,
        completedSteps: [],
        generatedContent: "",
        isTyping: false,
        progressPercentage: 0,
        targetProgress: 0,
        currentText: "",
        taskId: null, // 任务ID初始值为null

        // 表单数据初始值
        formData: null,
        showProgress: false,
        showResults: false,
        strategyData: null,

        // 更新方法
        updateCurrentStep: (step: number) =>
          set({ currentStep: step }, false, "updateCurrentStep"),

        updateCompletedSteps: (steps: number[]) =>
          set({ completedSteps: steps }, false, "updateCompletedSteps"),

        updateGeneratedContent: (content: string) =>
          set({ generatedContent: content }, false, "updateGeneratedContent"),

        updateIsTyping: (isTyping: boolean) =>
          set({ isTyping }, false, "updateIsTyping"),

        updateProgressPercentage: (percentage: number) =>
          set(
            { progressPercentage: percentage },
            false,
            "updateProgressPercentage"
          ),

        setProgressTarget: (target: number) =>
          set({ targetProgress: target }, false, "setProgressTarget"),

        setCurrentText: (text: string) =>
          set({ currentText: text }, false, "setCurrentText"),

        // 表单和流程相关方法
        setFormData: (data: FormData | null) =>
          set({ formData: data }, false, "setFormData"),

        setShowProgress: (show: boolean) =>
          set({ showProgress: show }, false, "setShowProgress"),

        setShowResults: (show: boolean) =>
          set({ showResults: show }, false, "setShowResults"),

        setStrategyData: (data: Array<StrategyData> | null) =>
          set({ strategyData: data }, false, "setStrategyData"),

        // 复合方法 - 开始进度流程
        startProgress: (formData: FormData, taskId: string) =>
          set(
            {
              formData,
              showProgress: true,
              showResults: false,
              currentStep: 0,
              completedSteps: [],
              taskId,
              progressPercentage: 0,
            },
            false,
            "startProgress"
          ),

        // 复合方法 - 完成进度流程
        completeProgress: (strategyData: Array<StrategyData>) =>
          set(
            {
              strategyData,
              showProgress: false,
              showResults: true,
            },
            false,
            "completeProgress"
          ),

        // 复合方法 - 重置所有状态
        resetState: () =>
          set(
            {
              showResults: false,
              showProgress: false,
              strategyData: null,
              formData: null,
              currentStep: 0,
              completedSteps: [],
              generatedContent: "",
              isTyping: false,
              progressPercentage: 0,
              targetProgress: 0,
              currentText: "",
            },
            false,
            "resetState"
          ),

        // 辅助方法
        addCompletedStep: (step: number) =>
          set(
            (state) => ({
              completedSteps: [...state.completedSteps, step],
            }),
            false,
            "addCompletedStep"
          ),

        resetProgress: () =>
          set(
            {
              currentStep: 0,
              completedSteps: [],
              generatedContent: "",
              isTyping: false,
              progressPercentage: 0,
              targetProgress: 0,
              currentText: "",
            },
            false,
            "resetProgress"
          ),
      }),
      {
        name: "progress-storage", // 存储的名称
        storage: createJSONStorage(() => sessionStorage), // 使用sessionStorage，也可以改为localStorage
        partialize: (state) => ({
          // 只持久化这些状态，不保存方法
          currentStep: state.currentStep,
          completedSteps: state.completedSteps,
          progressPercentage: state.progressPercentage,
          targetProgress: state.targetProgress,
          formData: state.formData,
          strategyData: state.strategyData,
        }),
      }
    )
  )
);

// 实用的选择器，可以获取部分状态
export const useProgressStep = () =>
  useProgressStore((state) => state.currentStep);
export const useCompletedSteps = () =>
  useProgressStore((state) => state.completedSteps);
export const useProgressPercentage = () =>
  useProgressStore((state) => state.progressPercentage);
export const useFormData = () => useProgressStore((state) => state.formData);
export const useShowProgress = () =>
  useProgressStore((state) => state.showProgress);
export const useShowResults = () =>
  useProgressStore((state) => state.showResults);
export const useStrategyData = () =>
  useProgressStore((state) => state.strategyData);
