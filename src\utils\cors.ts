


// 请求拦截器中的跨域处理
export const corsRequestInterceptor = (config: any) => {
  const isDev = import.meta.env.DEV;


  if (isDev) {
    // 开发环境：使用代理，确保URL格式正确
    if (config.url && !config.url.startsWith('http')) {
      // 确保以/api开头，这样会被Vite代理拦截
      if (!config.url.startsWith('/api')) {
        config.url = `/api${config.url.startsWith('/') ? '' : '/'}${config.url}`;
      }
    }

    // 开发环境不需要设置跨域headers，由代理处理
    config.headers = {
      ...config.headers,
      'X-Requested-With': 'XMLHttpRequest',
    };
  } else {
    // 生产环境：直接请求后端，需要处理跨域
    if (config.url && !config.url.startsWith('http')) {
      // 使用完整的API地址
      const baseURL = import.meta.env.VITE_API_BASE_URL || '';
      config.url = `${baseURL}${config.url.startsWith('/') ? '' : '/'}${config.url}`;
    }

    // 生产环境添加跨域相关headers
    config.headers = {
      ...config.headers,
      'X-Requested-With': 'XMLHttpRequest',
      'Access-Control-Request-Method': config.method?.toUpperCase(),
    };
  }

  return config;
};

// 响应拦截器中的跨域错误处理
export const corsResponseErrorHandler = (error: any) => {
  if (error.response?.status === 0 || error.code === 'ERR_NETWORK') {
    console.error('跨域请求失败，可能的原因：');
    console.error('1. 后端服务未启动');
    console.error('2. 后端未配置CORS');
    console.error('3. 代理配置错误');
    console.error('4. 网络连接问题');
    
    // 在开发环境给出更详细的提示
    if (import.meta.env.DEV) {
      console.error('开发环境解决方案：');
      console.error('1. 检查vite.config.ts中的proxy配置');
      console.error('2. 确认后端服务运行在正确的端口');
      console.error('3. 检查.env文件中的VITE_API_BASE_URL配置');
    }
  }
  
  return Promise.reject(error);
};

// 预检请求处理
export const handlePreflightRequest = () => {
  // 在某些情况下，可能需要手动处理OPTIONS请求
  // 这通常在后端处理，前端很少需要
  console.log('处理预检请求...');
};


// 跨域检测工具
export const checkCorsSupport = async (url: string): Promise<boolean> => {
  try {
    console.log(`[CORS] 检测跨域支持: ${url}`);
    const response = await fetch(url, {
      method: 'HEAD',
      mode: 'cors',
    });
    console.log(`[CORS] 检测结果: ${response.ok ? '支持' : '不支持'}`);
    return response.ok;
  } catch (error) {
    console.warn('[CORS] 检测失败:', error);
    return false;
  }
};

// 动态CORS配置
export const getDynamicCorsConfig = () => {
  const isDev = import.meta.env.DEV;
  const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;

  if (isDev) {
    // 开发环境：使用代理
    return {
      baseURL:'',
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    };
  } else {
    // 生产环境：直接请求后端API
    return {
      baseURL: apiBaseUrl || '',
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        // 生产环境可能需要的额外headers
        ...(typeof window !== 'undefined' && {
          'Origin': window.location.origin,
        }),
      },
    };
  }
};
