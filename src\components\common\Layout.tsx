import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ConfigProvider, theme, Layout as AntLayout } from 'antd';
import Sidebar from './Sidebar';
import Header from './Header';
import { colors } from '../../styles/colors';
import '../../styles/menu-animations.css';

const { Sider, Content } = AntLayout;

const Layout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 响应式检测
  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      if (mobile) {
        setCollapsed(true);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);



  const handleSelectHistory = (historyItem: any) => {
    // 这个功能需要在具体页面中实现
    console.log('History item selected:', historyItem);
  };

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          // 主色调 - 使用新的颜色对象结构
          colorPrimary: colors.primary.main,
          colorSuccess: colors.success.main,
          colorWarning: colors.warning.main,
          colorError: colors.error.main,
          colorInfo: colors.info.main,

          // 背景色 - 对应 Tailwind 的 app-* 类名
          colorBgBase: colors.background.base,        // #0f172a (app-base)
          colorBgContainer: colors.background.container, // #1e293b (app-container)
          colorBgElevated: colors.background.elevated,   // #334155 (app-elevated)
          colorBgLayout: colors.background.layout,

          // 边框和分割线 - 对应 Tailwind 的 app-* 类名
          colorBorder: colors.border.primary,         // #374151 (app-primary)
          colorBorderSecondary: colors.border.secondary, // #4b5563 (app-secondary)
          colorSplit: colors.border.split,

          // 文字颜色 - 对应 Tailwind 的 app-* 类名
          colorText: colors.text.primary,             // #f1f5f9 (app-primary)
          colorTextSecondary: colors.text.secondary,  // #94a3b8 (app-secondary)
          colorTextTertiary: colors.text.tertiary,    // #64748b (app-tertiary)
          colorTextQuaternary: colors.text.quaternary, // #475569 (app-quaternary)

          // 其他设计令牌
          borderRadius: 8,
          borderRadiusLG: 12,
          borderRadiusSM: 6,
          fontFamily: 'Inter, system-ui, sans-serif',
          fontSize: 14,
          lineHeight: 1.5,

          // 阴影 - 对应 Tailwind 的 shadow-app-* 类名
          boxShadow: colors.shadow.base,              // shadow-app-base
          boxShadowSecondary: colors.shadow.md,       // shadow-app-md
        },
        components: {
          Form: {
            labelColor: colors.text.primary,          // #f1f5f9 (text-app-primary)
            labelFontSize: 14,
            itemMarginBottom: 24,
          },
          Input: {
            colorBgContainer: colors.background.input,    // rgba(55, 65, 81, 0.5) (bg-app-input)
            colorBorder: colors.border.input,            // rgba(75, 85, 99, 0.5)
            colorText: colors.text.primary,              // #f1f5f9 (text-app-primary)
            colorTextPlaceholder: colors.text.placeholder, // #9ca3af (text-app-placeholder)
            borderRadius: 8,
            paddingBlock: 12,
            paddingInline: 16,
            activeBorderColor: colors.border.inputActive,  // #3b82f6 (primary)
            hoverBorderColor: colors.border.inputHover,   // #60a5fa (primary-400)
          },
          Button: {
            colorPrimary: colors.primary.main,           // #3b82f6 (bg-primary)
            borderRadius: 8,
            paddingBlock: 12,
            paddingInline: 16,
            fontWeight: 600,
          },
          Cascader: {
            colorBgContainer: colors.background.input,   // rgba(55, 65, 81, 0.5) (bg-app-input)
            colorBorder: colors.border.input,           // rgba(75, 85, 99, 0.5)
            colorText: colors.text.primary,             // #f1f5f9 (text-app-primary)
            borderRadius: 8,
            colorTextPlaceholder: colors.text.placeholder, // #9ca3af (text-app-placeholder)
          },
          Select: {
            selectorBg: colors.background.input,        // rgba(55, 65, 81, 0.5) (bg-app-input)
            colorBgContainer: colors.background.input,   // rgba(55, 65, 81, 0.5) (bg-app-input)
            colorBorder: colors.border.input,           // rgba(75, 85, 99, 0.5)
            colorText: colors.text.primary,             // #f1f5f9 (text-app-primary)
            borderRadius: 8,
            colorTextPlaceholder: colors.text.placeholder, // #9ca3af (text-app-placeholder)
          },
          Modal: {
            contentBg: colors.background.modal,         // rgba(17, 24, 39, 0.95) (bg-app-modal)
            headerBg: colors.background.modal,          // rgba(17, 24, 39, 0.95) (bg-app-modal)
            footerBg: colors.background.modal,          // rgba(17, 24, 39, 0.95) (bg-app-modal)
            borderRadius: 16,
          },
          Dropdown: {
            colorBgElevated: colors.background.dropdown, // rgba(31, 41, 55, 0.95)
            borderRadius: 12,
          },
          Layout: {
            bodyBg: 'transparent',
            headerBg: colors.background.header,         // rgba(17, 24, 39, 0.8) (bg-app-header)
            siderBg: colors.background.sidebar,         // rgba(17, 24, 39, 0.3) (bg-app-sidebar)
            triggerBg: colors.background.trigger,       // rgba(55, 65, 81, 0.5)
          },
          Menu: {
            darkItemBg: colors.menu.dark.itemBg,                // transparent
            darkItemSelectedBg: colors.menu.dark.itemSelectedBg, // transparent
            darkItemHoverBg: colors.menu.dark.itemHoverBg,      // rgba(55, 65, 81, 0.5)
            darkSubMenuItemBg: colors.menu.dark.subMenuItemBg,  // rgba(31, 41, 55, 0.5)
            darkItemColor: colors.menu.dark.itemColor,          // #94a3b8 (text-app-secondary)
            darkItemSelectedColor: colors.menu.dark.itemSelectedColor, // #ffffff
            darkItemHoverColor: colors.menu.dark.itemHoverColor,       // #f1f5f9 (text-app-primary)
            itemBorderRadius: 8,
            itemMarginBlock: 4,
            itemMarginInline: 8,
            itemPaddingInline: 16,
            subMenuItemBorderRadius: 6,
            itemHeight: 48,
          },
        },
      }}
    >
      <div className="min-h-screen bg-gradient-app-bg">
        {/* 移动端遮罩层 */}
        {isMobile && !collapsed && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-999"
            onClick={() => setCollapsed(true)}
          />
        )}

        <AntLayout className="min-h-screen bg-transparent">
          <Sider
            width={isMobile ? 0 : 280}
            collapsedWidth={isMobile ? 0 : 80}
            collapsed={collapsed}
            onCollapse={setCollapsed}
            className="bg-app-sidebar backdrop-blur-sm border-r border-app-light"
            theme="dark"
            breakpoint="lg"
            collapsible
            trigger={null}
            style={{
              overflow: 'auto',
              height: '100vh',
              position: 'fixed',
              left: 0,
              top: 0,
              bottom: 0,
              zIndex: isMobile ? 1000 : 'auto',
            }}
          >
            <Sidebar
              onSelectHistory={handleSelectHistory}
              collapsed={collapsed}
              isMobile={isMobile}
            />
          </Sider>

          <AntLayout
            className="bg-transparent"
            style={{
              marginLeft: isMobile ? 0 : (collapsed ? 80 : 280),
              transition: 'margin-left 0.2s',
            }}
          >
            <AntLayout.Header className="bg-transparent border-b border-app-light px-0 h-auto">
              <Header />
            </AntLayout.Header>

            <Content className="flex" style={{ minHeight: 'calc(100vh - 78px)' }}>
              <Outlet />
            </Content>
          </AntLayout>
        </AntLayout>
      </div>
    </ConfigProvider>
  );
};

export default Layout;
