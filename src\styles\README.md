# 项目颜色管理系统

本项目已经配置了完整的颜色管理系统，支持通过 Tailwind CSS 类名直接使用统一的颜色。

## 🎨 颜色系统概览

### 1. 主题色系
- **Primary (主色)**: 蓝色系 `#3b82f6`
- **Secondary (次要色)**: 青色系 `#06b6d4`  
- **Accent (强调色)**: 紫色系 `#9333ea`

### 2. 功能色系
- **Success (成功)**: 绿色系 `#10b981`
- **Warning (警告)**: 琥珀色系 `#f59e0b`
- **Error (错误)**: 红色系 `#ef4444`
- **Info (信息)**: 青色系 `#06b6d4`

### 3. 深色主题色系
- **Dark**: 石板色系，从 `#f8fafc` 到 `#020617`

## 🚀 使用方式

### 基础颜色类名

```html
<!-- 主题色 -->
<div class="bg-primary text-white">主色背景</div>
<div class="bg-secondary text-white">次要色背景</div>
<div class="bg-accent text-white">强调色背景</div>

<!-- 功能色 -->
<div class="bg-success text-white">成功状态</div>
<div class="bg-warning text-white">警告状态</div>
<div class="bg-error text-white">错误状态</div>
<div class="bg-info text-white">信息状态</div>

<!-- 深色主题 -->
<div class="bg-dark-900 text-dark-100">深色背景</div>
```

### 应用专用类名

```html
<!-- 背景色 -->
<div class="bg-app-base">应用基础背景</div>
<div class="bg-app-container">容器背景</div>
<div class="bg-app-elevated">悬浮背景</div>
<div class="bg-app-header">头部背景</div>
<div class="bg-app-sidebar">侧边栏背景</div>
<div class="bg-app-modal">模态框背景</div>
<div class="bg-app-input">输入框背景</div>

<!-- 文字色 -->
<p class="text-app-primary">主要文字</p>
<p class="text-app-secondary">次要文字</p>
<p class="text-app-tertiary">三级文字</p>
<p class="text-app-quaternary">四级文字</p>
<p class="text-app-placeholder">占位符文字</p>

<!-- 边框色 -->
<div class="border border-app-primary">主边框</div>
<div class="border border-app-secondary">次要边框</div>
<div class="border border-app-light">浅色边框</div>

<!-- 阴影 -->
<div class="shadow-app-base">基础阴影</div>
<div class="shadow-app-blue">蓝色阴影</div>
<div class="shadow-app-menu">菜单阴影</div>
```

### 渐变背景

```html
<!-- 主要渐变 -->
<div class="bg-gradient-primary">主要渐变</div>
<div class="bg-gradient-button">按钮渐变</div>

<!-- 卡片渐变 -->
<div class="bg-gradient-card-blue">蓝色卡片渐变</div>
<div class="bg-gradient-card-green">绿色卡片渐变</div>
<div class="bg-gradient-card-amber">琥珀色卡片渐变</div>
<div class="bg-gradient-card-pink">粉色卡片渐变</div>
<div class="bg-gradient-card-purple">紫色卡片渐变</div>

<!-- 应用背景 -->
<div class="bg-gradient-app-bg">应用背景渐变</div>
```

## 📁 文件结构

```
src/styles/
├── colors.ts              # TypeScript 颜色对象定义
├── css-variables.css      # CSS 变量定义
├── tailwind-usage.md      # Tailwind 使用指南
├── usage-examples.md      # 颜色对象使用示例
└── README.md              # 本文档
```

## 🔧 配置文件

### tailwind.config.js
已配置完整的自定义颜色系统，包括：
- 主题色 (primary, secondary, accent)
- 功能色 (success, warning, error, info)
- 深色主题色 (dark)
- 应用专用颜色 (app-*)
- 渐变背景 (gradient-*)
- 阴影效果 (shadow-app-*)

### 主要配置项
```javascript
theme: {
  extend: {
    colors: {
      primary: { /* 蓝色系 */ },
      secondary: { /* 青色系 */ },
      accent: { /* 紫色系 */ },
      // ... 其他颜色
    },
    backgroundColor: {
      'app-base': '#0f172a',
      'app-container': '#1e293b',
      // ... 其他背景色
    },
    textColor: {
      'app-primary': '#f1f5f9',
      'app-secondary': '#94a3b8',
      // ... 其他文字色
    },
    backgroundImage: {
      'gradient-primary': 'linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6)',
      // ... 其他渐变
    }
  }
}
```

## 🎯 最佳实践

### 1. 优先使用语义化类名
```html
<!-- ✅ 推荐 -->
<button class="bg-primary text-white">主要按钮</button>
<p class="text-app-secondary">次要文字</p>

<!-- ❌ 不推荐 -->
<button class="bg-blue-500 text-white">主要按钮</button>
<p class="text-gray-400">次要文字</p>
```

### 2. 保持一致性
```html
<!-- ✅ 同类型元素使用相同颜色 -->
<p class="text-app-secondary">描述文字 1</p>
<p class="text-app-secondary">描述文字 2</p>

<!-- ✅ 状态色使用统一 -->
<div class="bg-success text-white">成功消息</div>
<div class="text-success">成功文字</div>
```

### 3. 合理使用色阶
```html
<!-- ✅ 根据重要性选择色阶 -->
<h1 class="text-primary-600">重要标题</h1>
<h2 class="text-primary-500">次要标题</h2>
<p class="text-primary-400">普通文字</p>
```

### 4. 响应式设计
```html
<!-- ✅ 响应式颜色 -->
<div class="bg-primary md:bg-secondary lg:bg-accent">
  响应式背景色
</div>
```

## 🔄 迁移指南

### 从内联样式迁移到 Tailwind 类名

```html
<!-- 旧方式 -->
<div style="background-color: #1e293b; color: #f1f5f9;">
  内容
</div>

<!-- 新方式 -->
<div class="bg-app-container text-app-primary">
  内容
</div>
```

### 从 CSS 变量迁移到 Tailwind 类名

```css
/* 旧方式 */
.custom-element {
  background-color: var(--bg-container);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}
```

```html
<!-- 新方式 -->
<div class="bg-app-container text-app-primary border border-app-primary">
  内容
</div>
```

## 📚 相关文档

- [Tailwind CSS 使用指南](./tailwind-usage.md) - 详细的 Tailwind 类名使用说明
- [颜色对象使用示例](./usage-examples.md) - TypeScript 颜色对象的使用方法
- [CSS 变量定义](./css-variables.css) - 完整的 CSS 变量列表

## 🎨 颜色预览

### 主题色
- 🔵 Primary: `bg-primary` - 主要品牌色
- 🔷 Secondary: `bg-secondary` - 次要品牌色  
- 🟣 Accent: `bg-accent` - 强调色

### 功能色
- 🟢 Success: `bg-success` - 成功状态
- 🟡 Warning: `bg-warning` - 警告状态
- 🔴 Error: `bg-error` - 错误状态
- 🔵 Info: `bg-info` - 信息状态

### 应用色
- ⚫ App Base: `bg-app-base` - 应用基础背景
- ⬛ App Container: `bg-app-container` - 容器背景
- 🔘 App Elevated: `bg-app-elevated` - 悬浮背景

现在你可以直接使用这些 Tailwind 类名，而不需要内联样式或 CSS 变量！
