# Ant Design 主题配置说明

本文档说明了项目中 Ant Design 主题配置与颜色系统的对应关系。

## 🎨 主题配置概览

### Token 配置 (全局设计令牌)

| Token 名称 | 颜色对象 | 颜色值 | Tailwind 类名 | 说明 |
|-----------|---------|--------|---------------|------|
| `colorPrimary` | `colors.primary.main` | `#3b82f6` | `bg-primary` | 主色调 |
| `colorSuccess` | `colors.success.main` | `#10b981` | `bg-success` | 成功色 |
| `colorWarning` | `colors.warning.main` | `#f59e0b` | `bg-warning` | 警告色 |
| `colorError` | `colors.error.main` | `#ef4444` | `bg-error` | 错误色 |
| `colorInfo` | `colors.info.main` | `#06b6d4` | `bg-info` | 信息色 |

### 背景色配置

| Token 名称 | 颜色对象 | 颜色值 | Tailwind 类名 | 说明 |
|-----------|---------|--------|---------------|------|
| `colorBgBase` | `colors.background.base` | `#0f172a` | `bg-app-base` | 应用基础背景 |
| `colorBgContainer` | `colors.background.container` | `#1e293b` | `bg-app-container` | 容器背景 |
| `colorBgElevated` | `colors.background.elevated` | `#334155` | `bg-app-elevated` | 悬浮背景 |
| `colorBgLayout` | `colors.background.layout` | `#0f172a` | `bg-app-base` | 布局背景 |

### 文字色配置

| Token 名称 | 颜色对象 | 颜色值 | Tailwind 类名 | 说明 |
|-----------|---------|--------|---------------|------|
| `colorText` | `colors.text.primary` | `#f1f5f9` | `text-app-primary` | 主要文字 |
| `colorTextSecondary` | `colors.text.secondary` | `#94a3b8` | `text-app-secondary` | 次要文字 |
| `colorTextTertiary` | `colors.text.tertiary` | `#64748b` | `text-app-tertiary` | 三级文字 |
| `colorTextQuaternary` | `colors.text.quaternary` | `#475569` | `text-app-quaternary` | 四级文字 |

### 边框色配置

| Token 名称 | 颜色对象 | 颜色值 | Tailwind 类名 | 说明 |
|-----------|---------|--------|---------------|------|
| `colorBorder` | `colors.border.primary` | `#374151` | `border-app-primary` | 主边框 |
| `colorBorderSecondary` | `colors.border.secondary` | `#4b5563` | `border-app-secondary` | 次要边框 |
| `colorSplit` | `colors.border.split` | `#374151` | `border-app-primary` | 分割线 |

### 阴影配置

| Token 名称 | 颜色对象 | Tailwind 类名 | 说明 |
|-----------|---------|---------------|------|
| `boxShadow` | `colors.shadow.base` | `shadow-app-base` | 基础阴影 |
| `boxShadowSecondary` | `colors.shadow.md` | `shadow-app-md` | 中等阴影 |

## 🧩 组件配置

### Form 组件
```typescript
Form: {
  labelColor: colors.text.primary,          // #f1f5f9 (text-app-primary)
  labelFontSize: 14,
  itemMarginBottom: 24,
}
```

### Input 组件
```typescript
Input: {
  colorBgContainer: colors.background.input,    // rgba(55, 65, 81, 0.5) (bg-app-input)
  colorBorder: colors.border.input,            // rgba(75, 85, 99, 0.5)
  colorText: colors.text.primary,              // #f1f5f9 (text-app-primary)
  colorTextPlaceholder: colors.text.placeholder, // #9ca3af (text-app-placeholder)
  activeBorderColor: colors.border.inputActive,  // #3b82f6 (primary)
  hoverBorderColor: colors.border.inputHover,   // #60a5fa (primary-400)
}
```

### Button 组件
```typescript
Button: {
  colorPrimary: colors.primary.main,           // #3b82f6 (bg-primary)
  borderRadius: 8,
  paddingBlock: 12,
  paddingInline: 16,
  fontWeight: 600,
}
```

### Select & Cascader 组件
```typescript
Select: {
  selectorBg: colors.background.input,        // rgba(55, 65, 81, 0.5) (bg-app-input)
  colorBgContainer: colors.background.input,   // rgba(55, 65, 81, 0.5) (bg-app-input)
  colorBorder: colors.border.input,           // rgba(75, 85, 99, 0.5)
  colorText: colors.text.primary,             // #f1f5f9 (text-app-primary)
}
```

### Modal 组件
```typescript
Modal: {
  contentBg: colors.background.modal,         // rgba(17, 24, 39, 0.95) (bg-app-modal)
  headerBg: colors.background.modal,          // rgba(17, 24, 39, 0.95) (bg-app-modal)
  footerBg: colors.background.modal,          // rgba(17, 24, 39, 0.95) (bg-app-modal)
  borderRadius: 16,
}
```

### Dropdown 组件
```typescript
Dropdown: {
  colorBgElevated: colors.background.dropdown, // rgba(31, 41, 55, 0.95)
  borderRadius: 12,
}
```

### Layout 组件
```typescript
Layout: {
  bodyBg: 'transparent',
  headerBg: colors.background.header,         // rgba(17, 24, 39, 0.8) (bg-app-header)
  siderBg: colors.background.sidebar,         // rgba(17, 24, 39, 0.3) (bg-app-sidebar)
  triggerBg: colors.background.trigger,       // rgba(55, 65, 81, 0.5)
}
```

### Menu 组件 (深色主题)
```typescript
Menu: {
  darkItemBg: colors.menu.dark.itemBg,                // transparent
  darkItemSelectedBg: colors.menu.dark.itemSelectedBg, // transparent
  darkItemHoverBg: colors.menu.dark.itemHoverBg,      // rgba(55, 65, 81, 0.5)
  darkSubMenuItemBg: colors.menu.dark.subMenuItemBg,  // rgba(31, 41, 55, 0.5)
  darkItemColor: colors.menu.dark.itemColor,          // #94a3b8 (text-app-secondary)
  darkItemSelectedColor: colors.menu.dark.itemSelectedColor, // #ffffff
  darkItemHoverColor: colors.menu.dark.itemHoverColor,       // #f1f5f9 (text-app-primary)
  itemBorderRadius: 8,
  itemMarginBlock: 4,
  itemMarginInline: 8,
  itemPaddingInline: 16,
  subMenuItemBorderRadius: 6,
}
```

## 🎯 配置优势

### 1. **统一性**
- 所有组件使用相同的颜色系统
- 与 Tailwind CSS 类名完全对应
- 便于维护和更新

### 2. **一致性**
- 深色主题下的视觉一致性
- 组件间的颜色协调
- 符合设计规范

### 3. **可维护性**
- 集中管理颜色配置
- 易于主题切换
- 便于批量更新

### 4. **开发体验**
- 清晰的注释说明
- 与 Tailwind 类名对应
- 便于理解和使用

## 🔧 自定义配置

如需修改主题配置，请按以下步骤：

1. **更新颜色对象** (`src/styles/colors.ts`)
2. **更新 Tailwind 配置** (`tailwind.config.js`)
3. **更新主题配置** (`src/App.tsx`)
4. **更新 CSS 变量** (`src/styles/css-variables.css`)

### 示例：添加新的主题色
```typescript
// 1. 在 colors.ts 中添加
export const colors = {
  // ... 现有颜色
  brand: {
    main: '#your-color',
    light: '#your-light-color',
    dark: '#your-dark-color',
  },
};

// 2. 在 tailwind.config.js 中添加
theme: {
  extend: {
    colors: {
      brand: {
        500: '#your-color',
        // ... 其他色阶
      },
    },
  },
},

// 3. 在 App.tsx 主题配置中使用
token: {
  colorBrand: colors.brand.main,
},
```

## 📚 相关文档

- [颜色对象定义](./colors.ts) - 完整的颜色对象定义
- [Tailwind 配置](../tailwind.config.js) - Tailwind CSS 自定义配置
- [颜色映射关系](./color-mapping.md) - 颜色对象与 Tailwind 类名对应关系
- [使用指南](./README.md) - 颜色系统使用指南

通过这套配置，项目实现了统一、一致、可维护的主题系统！
