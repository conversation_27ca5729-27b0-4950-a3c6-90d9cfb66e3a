import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
} from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  BookOpen,
  Lightbulb,
  Target,
  Users,
  UserCheck,
  Loader2,
  CheckCircle,
  Sparkles,
} from "lucide-react";
import { useFormData, useProgressStore } from "@/contexts/ProgressContext";


const ProgressDisplay: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const timeoutIdRef = useRef<number | null>(null);
  const currentTextRef = useRef<string>("");
  const currentCallbackRef = useRef<(() => void) | undefined>();
  const pageHiddenTimeRef = useRef<number | null>(null);
  const currentStepStartTimeRef = useRef<number>(Date.now());
  const formData = useFormData();
  const { completeProgress } = useProgressStore();

  const progressSteps = useMemo(
    () => [
      {
        id: 0,
        title: "正在解读营销需求",
        icon: Search,
        color: "from-blue-500 to-cyan-500",
        duration: 2000,
        content: `正在分析您的品牌"${formData?.brand}"的营销需求...\n\n✓ 品牌定位分析完成\n✓ 目标受众"${formData?.audience}"特征识别\n✓ 预算范围"${formData?.budget}"评估完成`,
      },
      {
        id: 1,
        title: "正在查阅品牌行业资料",
        icon: BookOpen,
        color: "from-green-500 to-emerald-500",
        duration: 2500,
        content: `正在深度研究"${formData?.industry}"行业趋势...\n\n✓ 行业竞争格局分析\n✓ 消费者行为模式研究\n✓ 市场机会点识别\n✓ 竞品"${formData?.competitors}"策略分析`,
      },
      {
        id: 2,
        title: "正在生成整体性创意",
        icon: Lightbulb,
        color: "from-yellow-500 to-orange-500",
        duration: 3000,
        content: `基于"${formData?.features}"核心卖点生成创意方案...\n\n✓ 品牌故事线构建\n✓ 视觉创意方向确定\n✓ 内容创意框架设计\n✓ 传播节奏规划完成`,
      },
      {
        id: 3,
        title: "正在制定营销策略方案",
        icon: Target,
        color: "from-purple-500 to-pink-500",
        duration: 2800,
        content: `正在制定精准的营销投放策略...\n\n✓ 平台选择策略制定\n✓ 投放时间节点规划\n✓ 预算分配方案优化\n✓ ROI预期目标设定`,
      },
      {
        id: 4,
        title: "正在制定达人策略",
        icon: Users,
        color: "from-indigo-500 to-purple-500",
        duration: 2200,
        content: `正在设计达人合作策略矩阵...\n\n✓ 达人分层策略制定\n✓ KOL/KOC配比优化\n✓ 合作模式设计完成\n✓ 效果评估体系建立`,
      },
      {
        id: 5,
        title: "正在推荐候选达人",
        icon: UserCheck,
        color: "from-rose-500 to-red-500",
        duration: 2000,
        content: `正在筛选最适合的达人候选...\n\n✓ 达人数据库匹配完成\n✓ 垂直度评估完成\n✓ 性价比分析完成\n✓ 推荐清单生成完成`,
      },
    ],
    [
      formData?.brand,
      formData?.audience,
      formData?.budget,
      formData?.industry,
      formData?.competitors,
      formData?.features,
    ]
  );

  // 根据离开时间计算应该进行到的步骤和字符位置
  const calculateProgressByTime = useCallback(
    (awayTime: number) => {
      const typeSpeed = 30; // 每个字符的间隔时间（毫秒）
      const stepDelay = 800; // 每步完成后的延迟时间（毫秒）

      let totalElapsedTime = 0;
      let targetStep = currentStep;
      let targetCharIndex = 0;

      // 从当前步骤开始计算
      for (
        let stepIndex = currentStep;
        stepIndex < progressSteps.length;
        stepIndex++
      ) {
        const step = progressSteps[stepIndex];
        const stepTextLength = step.content.length;
        const stepTypingTime = stepTextLength * typeSpeed;
        const totalStepTime = stepTypingTime + stepDelay;

        if (totalElapsedTime + totalStepTime <= awayTime) {
          // 这一步可以完全完成
          totalElapsedTime += totalStepTime;
          targetStep = stepIndex + 1;
          targetCharIndex = 0;
        } else {
          // 这一步只能部分完成
          const remainingTime = awayTime - totalElapsedTime;
          if (remainingTime <= stepTypingTime) {
            // 还在打字阶段
            targetStep = stepIndex;
            targetCharIndex = Math.floor(remainingTime / typeSpeed);
          } else {
            // 打字完成，在等待阶段
            targetStep = stepIndex;
            targetCharIndex = stepTextLength; // 显示完整文本
          }
          break;
        }
      }

      return {
        targetStep: Math.min(targetStep, progressSteps.length - 1),
        targetCharIndex,
        shouldCompleteCurrentStep:
          targetCharIndex >= (progressSteps[targetStep]?.content.length || 0),
      };
    },
    [currentStep, progressSteps]
  );

  // 从指定位置继续打字
  const continueTypingFrom = useCallback(
    (startIndex: number, text: string, callback?: () => void) => {
      // 清理之前的定时器
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      let index = startIndex;
      const typeSpeed = 30; // 每个字符的间隔时间（毫秒）

      const typeNextChar = () => {
        if (index < text.length) {
          setGeneratedContent(text.substring(0, index + 1));
          index++;
          timeoutIdRef.current = window.setTimeout(typeNextChar, typeSpeed);
        } else {
          // 打字完成
          setIsTyping(false);
          timeoutIdRef.current = null;
          currentTextRef.current = "";
          currentCallbackRef.current = undefined;
          if (callback) callback();
        }
      };

      if (index < text.length) {
        setIsTyping(true);
        typeNextChar();
      } else {
        // 已经完成
        setIsTyping(false);
        if (callback) callback();
      }
    },
    []
  );

  // 完成当前打字动画的函数
  const completeCurrentTyping = useCallback(() => {
    if (isTyping && currentTextRef.current) {
      // 清理定时器
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }

      // 立即显示完整文本
      setGeneratedContent(currentTextRef.current);
      setIsTyping(false);

      // 执行回调
      if (currentCallbackRef.current) {
        const callback = currentCallbackRef.current;
        currentCallbackRef.current = undefined;
        currentTextRef.current = "";
        callback();
      }
    }
  }, [isTyping]);

  const typeWriter = useCallback((text: string, callback?: () => void) => {
    // 清理之前的定时器
    if (timeoutIdRef.current) {
      clearTimeout(timeoutIdRef.current);
      timeoutIdRef.current = null;
    }

    // 保存当前文本和回调，用于页面可见性变化时快速完成
    currentTextRef.current = text;
    currentCallbackRef.current = callback;

    setIsTyping(true);
    setGeneratedContent("");

    let index = 0;
    const typeSpeed = 30; // 每个字符的间隔时间（毫秒）

    const typeNextChar = () => {
      if (index < text.length) {
        setGeneratedContent((prev) => prev + text.charAt(index));
        index++;

        // 使用 setTimeout 而不是 requestAnimationFrame
        // 这样即使在后台标签页也会继续执行，模拟真实的流式输出
        timeoutIdRef.current = window.setTimeout(typeNextChar, typeSpeed);
      } else {
        // 打字完成
        setIsTyping(false);
        timeoutIdRef.current = null;
        currentTextRef.current = "";
        currentCallbackRef.current = undefined;
        if (callback) callback();
      }
    };

    // 开始打字
    typeNextChar();
  }, []);

  // 页面可见性变化检测
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时记录时间
        pageHiddenTimeRef.current = Date.now();
      } else {
        // 页面显示时计算离开时间并更新进度
        if (pageHiddenTimeRef.current && isTyping) {
          const awayTime = Date.now() - pageHiddenTimeRef.current;
          const { targetStep, targetCharIndex, shouldCompleteCurrentStep } =
            calculateProgressByTime(awayTime);

          // 清理当前的定时器
          if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
            timeoutIdRef.current = null;
          }

          // 更新到计算出的进度
          if (targetStep > currentStep) {
            // 需要跳到更后面的步骤
            // 先完成之前的所有步骤
            const stepsToComplete: number[] = [];
            for (let i = currentStep; i < targetStep; i++) {
              stepsToComplete.push(i);
            }
            setCompletedSteps((prev) => [...prev, ...stepsToComplete]);
            setCurrentStep(targetStep);

            if (targetStep < progressSteps.length) {
              // 开始新步骤的打字，从计算出的字符位置开始
              const newStepText = progressSteps[targetStep].content;
              const displayText = newStepText.substring(0, targetCharIndex);
              setGeneratedContent(displayText);

              if (shouldCompleteCurrentStep) {
                // 这一步也应该完成
                setGeneratedContent(newStepText);
                setIsTyping(false);
                setTimeout(() => {
                  setCompletedSteps((prev) => [...prev, targetStep]);
                  if (targetStep < progressSteps.length - 1) {
                    setCurrentStep(targetStep + 1);
                  }
                }, 800);
              } else {
                // 从当前位置继续打字
                currentTextRef.current = newStepText;
                continueTypingFrom(
                  targetCharIndex,
                  newStepText,
                  currentCallbackRef.current
                );
              }
            }
          } else if (targetStep === currentStep) {
            // 在当前步骤内，更新字符位置
            const currentStepText = progressSteps[currentStep].content;
            const displayText = currentStepText.substring(0, targetCharIndex);
            setGeneratedContent(displayText);

            if (shouldCompleteCurrentStep) {
              completeCurrentTyping();
            } else {
              // 从当前位置继续打字
              continueTypingFrom(
                targetCharIndex,
                currentStepText,
                currentCallbackRef.current
              );
            }
          }

          pageHiddenTimeRef.current = null;
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [
    isTyping,
    completeCurrentTyping,
    calculateProgressByTime,
    currentStep,
    progressSteps,
    continueTypingFrom,
  ]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (currentStep < progressSteps.length) {
      const step = progressSteps[currentStep];

      // 记录当前步骤开始时间
      currentStepStartTimeRef.current = Date.now();

      // 开始打字效果
      typeWriter(step.content, () => {
        // 打字完成后等待一段时间，然后标记步骤完成
        setCompletedSteps((prev) => [...prev, currentStep]);
        if (currentStep < progressSteps.length - 1) {
          setCurrentStep(currentStep + 1);
        } else {
          // 所有步骤完成，生成最终结果
          setTimeout(() => {
            // TODO: 获取三个计划
            // 模拟获取数据
            completeProgress([
              {
                content: `基于您的品牌"${formData?.brand}"定位和目标受众"${formData?.audience}"分析，建议采用多平台差异化投放策略。重点聚焦小红书和抖音平台，通过KOC种草+KOL扩散的组合打法，实现品牌认知度提升和销量转化双重目标。`,
              },
            ]);
            // onComplete({
            //   brand: formData?.brand,
            //   audience: formData?.audience,
            //   competitors: formData?.competitors,
            //   industry: formData?.industry,
            //   budget: formData?.budget,
            //   features: formData?.features,
            //   coreStrategy: `基于您的品牌"${formData?.brand}"定位和目标受众"${formData?.audience}"分析，建议采用多平台差异化投放策略。重点聚焦小红书和抖音平台，通过KOC种草+KOL扩散的组合打法，实现品牌认知度提升和销量转化双重目标。`,
            //   contentStrategy:
            //     "内容策略需要围绕产品核心卖点，打造多层次内容矩阵...",
            //   influencerStrategy:
            //     "达人筛选应基于垂直度、粉丝质量和转化能力三个维度...",
            // });
          }, 1000);
        }
      });
    }
  }, [currentStep, progressSteps, typeWriter, formData]);

  return (
    <div className="h-full flex">
      {/* 左侧进度区域 */}
      <div className="w-96 p-6 border-r border-gray-800/50">
        <div className="mb-6">
          <h3 className="text-xl font-bold text-white mb-2">AI策略生成中</h3>
          <p className="text-gray-400 text-sm">
            请稍候，AI正在为您生成专业的广告策略...
          </p>
        </div>

        <div className="space-y-4 overflow-hidden">
          {progressSteps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className={`relative p-4 rounded-xl border transition-all duration-500 ${
                completedSteps.includes(step.id)
                  ? `bg-gradient-to-r ${step.color} border-transparent shadow-lg`
                  : currentStep === step.id
                  ? "bg-gray-800/50 border-gray-600/50 backdrop-blur-sm"
                  : "bg-gray-900/30 border-gray-800/50"
              }`}
            >
              <div className="flex items-center space-x-3">
                <div
                  className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                    completedSteps.includes(step.id)
                      ? "bg-white/20"
                      : currentStep === step.id
                      ? "bg-blue-500/20"
                      : "bg-gray-700/50"
                  }`}
                >
                  {completedSteps.includes(step.id) ? (
                    <CheckCircle className="w-5 h-5 text-white" />
                  ) : currentStep === step.id ? (
                    <Loader2 className="w-5 h-5 text-blue-400 animate-spin" />
                  ) : (
                    <step.icon className="w-5 h-5 text-gray-500" />
                  )}
                </div>

                <div className="flex-1">
                  <h4
                    className={`font-medium text-sm ${
                      completedSteps.includes(step.id) ||
                      currentStep === step.id
                        ? "text-white"
                        : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </h4>
                  <div
                    className={`text-xs mt-1 ${
                      completedSteps.includes(step.id)
                        ? "text-white/80"
                        : currentStep === step.id
                        ? "text-blue-400"
                        : "text-gray-600"
                    }`}
                  >
                    {completedSteps.includes(step.id)
                      ? "✓ 完成"
                      : currentStep === step.id
                      ? "进行中..."
                      : "等待中"}
                  </div>
                </div>
              </div>

              {/* 进度条 */}
              {currentStep === step.id && (
                <motion.div
                  className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-b-xl"
                  initial={{ width: 0 }}
                  animate={{ width: "100%" }}
                  transition={{
                    duration: (step.content.length * 30 + 800) / 1000,
                    ease: "linear",
                  }}
                  style={{ willChange: "auto" }}
                />
              )}
            </motion.div>
          ))}
        </div>

        {/* 总体进度 */}
        <div className="mt-6 p-4 bg-gray-800/30 rounded-xl border border-gray-700/50">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">总体进度</span>
            <span className="text-sm text-blue-400 font-medium">
              {Math.round(
                (progressSteps
                  .filter((step) => completedSteps.includes(step.id))
                  .map((step) => step.content)
                  .flat().length /
                  progressSteps.map((step) => step.content).flat().length) *
                  100
              )}
              %
            </span>
          </div>
          <div className="w-full bg-gray-700/50 rounded-full h-2">
            <motion.div
              className="h-2 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 rounded-full"
              initial={{ width: 0 }}
              animate={{
                width: `calc(${
                  ((completedSteps.length +
                    (currentStep < progressSteps.length ? 0.5 : 0)) /
                    progressSteps.length) *
                  100
                }% - 18px)`, // 减去左右padding的宽度
              }}
              transition={{ duration: 0.5 }}
              style={{ willChange: "auto" }}
            />
          </div>
        </div>
      </div>

      {/* 右侧实时内容输出区域 */}
      <div className="flex-1 p-6">
        <div className="h-full flex flex-col">
          <div className="mb-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Sparkles className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">AI实时分析</h3>
                <p className="text-gray-400 text-sm">观察AI的思考过程</p>
              </div>
            </div>
          </div>

          <div className="flex-1 bg-gray-900/50 backdrop-blur-sm rounded-xl border border-gray-800/50 p-6 overflow-y-auto">
            <div className="font-mono text-sm leading-relaxed">
              <AnimatePresence>
                {generatedContent && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-gray-300 whitespace-pre-line"
                    style={{ willChange: "auto" }}
                  >
                    {generatedContent}
                  </motion.div>
                )}
              </AnimatePresence>

              {isTyping && (
                <motion.span
                  animate={{ opacity: [1, 0] }}
                  transition={{ duration: 0.8, repeat: Infinity }}
                  className="inline-block w-2 h-5 bg-blue-400 ml-1"
                  style={{ willChange: "auto" }}
                />
              )}
            </div>
          </div>

          {currentStep < progressSteps.length && (
            <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
              <div className="flex items-center space-x-2 text-blue-400">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span className="text-sm font-medium">
                  {progressSteps[currentStep]?.title}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProgressDisplay;
