{"name": "ai-duomai-advertising-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development", "dev:test": "vite --host --mode test", "dev:prod": "vite --host --mode production", "build": "vite build --mode development", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production --sourcemap && echo 分析报告已生成在 dist/stats.html", "build:optimized": "vite build --mode production --config vite.config.ts", "analyze:deps": "node dependency-analyzer.mjs", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview --mode development", "preview:test": "vite preview --mode test", "preview:prod": "vite preview --mode production"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.26.1", "axios": "^1.10.0", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.2", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^24.0.3", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}