# Tailwind CSS 自定义颜色使用指南

本文档展示如何使用项目中配置的 Tailwind CSS 自定义颜色类名。

## 1. 主题色类名

### 主色调 (Primary)
```html
<!-- 背景色 -->
<div class="bg-primary">主色背景</div>
<div class="bg-primary-500">主色背景 (明确色阶)</div>
<div class="bg-primary-100">浅色背景</div>
<div class="bg-primary-900">深色背景</div>

<!-- 文字色 -->
<p class="text-primary">主色文字</p>
<p class="text-primary-600">深一点的主色文字</p>

<!-- 边框色 -->
<div class="border border-primary">主色边框</div>
```

### 次要色 (Secondary)
```html
<div class="bg-secondary text-white">次要色背景</div>
<p class="text-secondary">次要色文字</p>
<div class="border border-secondary-300">次要色边框</div>
```

### 强调色 (Accent)
```html
<button class="bg-accent text-white">强调色按钮</button>
<p class="text-accent-600">强调色文字</p>
```

## 2. 功能色类名

### 成功色
```html
<div class="bg-success text-white">成功状态</div>
<p class="text-success">成功文字</p>
<div class="border border-success-300">成功边框</div>
```

### 警告色
```html
<div class="bg-warning text-white">警告状态</div>
<p class="text-warning">警告文字</p>
```

### 错误色
```html
<div class="bg-error text-white">错误状态</div>
<p class="text-error">错误文字</p>
```

### 信息色
```html
<div class="bg-info text-white">信息状态</div>
<p class="text-info">信息文字</p>
```

## 3. 深色主题色类名

```html
<!-- 背景色 -->
<div class="bg-dark-900">最深背景</div>
<div class="bg-dark-800">容器背景</div>
<div class="bg-dark-700">悬浮背景</div>

<!-- 文字色 -->
<p class="text-dark-100">主要文字</p>
<p class="text-dark-400">次要文字</p>
<p class="text-dark-500">三级文字</p>
```

## 4. 应用专用类名

### 背景色
```html
<div class="bg-app-base">应用基础背景</div>
<div class="bg-app-container">容器背景</div>
<div class="bg-app-elevated">悬浮背景</div>
<div class="bg-app-header">头部背景</div>
<div class="bg-app-sidebar">侧边栏背景</div>
<div class="bg-app-modal">模态框背景</div>
<div class="bg-app-input">输入框背景</div>
```

### 文字色
```html
<p class="text-app-primary">主要文字</p>
<p class="text-app-secondary">次要文字</p>
<p class="text-app-tertiary">三级文字</p>
<p class="text-app-quaternary">四级文字</p>
<p class="text-app-placeholder">占位符文字</p>
```

### 边框色
```html
<div class="border border-app-primary">主边框</div>
<div class="border border-app-secondary">次要边框</div>
<div class="border border-app-light">浅色边框</div>
```

### 阴影
```html
<div class="shadow-app-sm">小阴影</div>
<div class="shadow-app-base">基础阴影</div>
<div class="shadow-app-md">中等阴影</div>
<div class="shadow-app-blue">蓝色阴影</div>
<div class="shadow-app-menu">菜单阴影</div>
```

## 5. 渐变背景

```html
<!-- 主要渐变 -->
<div class="bg-gradient-primary">主要渐变</div>
<div class="bg-gradient-primary-hover">主要渐变悬停</div>

<!-- 按钮渐变 -->
<button class="bg-gradient-button text-white">渐变按钮</button>
<button class="bg-gradient-button-hover text-white">渐变按钮悬停</button>

<!-- 卡片渐变 -->
<div class="bg-gradient-card-blue">蓝色卡片渐变</div>
<div class="bg-gradient-card-green">绿色卡片渐变</div>
<div class="bg-gradient-card-amber">琥珀色卡片渐变</div>
<div class="bg-gradient-card-pink">粉色卡片渐变</div>
<div class="bg-gradient-card-purple">紫色卡片渐变</div>

<!-- 应用背景渐变 -->
<div class="bg-gradient-app-bg">应用背景渐变</div>
```

## 6. 组合使用示例

### 卡片组件
```html
<div class="bg-app-container border border-app-light rounded-xl p-6 shadow-app-base">
  <h3 class="text-app-primary text-xl font-bold mb-2">卡片标题</h3>
  <p class="text-app-secondary mb-4">卡片描述文字</p>
  <button class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors">
    操作按钮
  </button>
</div>
```

### 状态消息
```html
<!-- 成功消息 -->
<div class="bg-success-100 border border-success-300 text-success-800 p-4 rounded-lg">
  操作成功！
</div>

<!-- 错误消息 -->
<div class="bg-error-100 border border-error-300 text-error-800 p-4 rounded-lg">
  操作失败，请重试。
</div>

<!-- 警告消息 -->
<div class="bg-warning-100 border border-warning-300 text-warning-800 p-4 rounded-lg">
  请注意相关事项。
</div>
```

### 导航菜单
```html
<nav class="bg-app-sidebar border-r border-app-light">
  <div class="p-6 border-b border-app-light">
    <h1 class="text-white text-xl font-bold">多麦AI</h1>
    <p class="text-app-secondary text-sm mt-1">智能广告策略平台</p>
  </div>
  
  <div class="p-4">
    <a href="#" class="block text-app-secondary hover:text-app-primary hover:bg-app-elevated p-3 rounded-lg transition-colors">
      策略生成
    </a>
    <a href="#" class="block text-app-secondary hover:text-app-primary hover:bg-app-elevated p-3 rounded-lg transition-colors">
      情感分析
    </a>
  </div>
</nav>
```

### 表单组件
```html
<form class="space-y-4">
  <div>
    <label class="block text-app-primary text-sm font-medium mb-2">
      品牌名称
    </label>
    <input 
      type="text" 
      class="w-full bg-app-input border border-app-light text-app-primary placeholder-app-placeholder rounded-lg px-4 py-3 focus:border-primary focus:ring-1 focus:ring-primary transition-colors"
      placeholder="请输入品牌名称"
    />
  </div>
  
  <button 
    type="submit" 
    class="w-full bg-gradient-button text-white font-semibold py-3 px-4 rounded-lg shadow-app-menu hover:shadow-app-blue-hover transition-all duration-300"
  >
    生成策略
  </button>
</form>
```

## 7. 响应式使用

```html
<!-- 响应式背景色 -->
<div class="bg-app-container md:bg-app-elevated lg:bg-app-base">
  响应式背景
</div>

<!-- 响应式文字色 -->
<p class="text-app-secondary md:text-app-primary lg:text-primary">
  响应式文字颜色
</p>

<!-- 响应式渐变 -->
<div class="bg-primary md:bg-gradient-primary lg:bg-gradient-button">
  响应式渐变背景
</div>
```

## 8. 暗色模式支持

如果需要支持暗色模式，可以使用 `dark:` 前缀：

```html
<div class="bg-white dark:bg-app-container text-gray-900 dark:text-app-primary">
  自动适应暗色模式的内容
</div>

<button class="bg-gray-200 dark:bg-app-elevated text-gray-800 dark:text-app-primary">
  自适应按钮
</button>
```

## 9. 最佳实践

1. **优先使用语义化类名**：使用 `text-primary` 而不是 `text-blue-500`
2. **保持一致性**：同类型元素使用相同的颜色类名
3. **合理使用色阶**：根据重要性选择合适的色阶 (100-900)
4. **注意对比度**：确保文字和背景有足够的对比度
5. **响应式设计**：在不同屏幕尺寸下使用合适的颜色

## 10. 与原有颜色对象的对应关系

| 颜色对象 | Tailwind 类名 |
|---------|---------------|
| `colors.primary.main` | `bg-primary` 或 `text-primary` |
| `colors.text.primary` | `text-app-primary` |
| `colors.text.secondary` | `text-app-secondary` |
| `colors.background.container` | `bg-app-container` |
| `colors.border.primary` | `border-app-primary` |
| `colors.shadow.base` | `shadow-app-base` |
| `colors.gradients.primary` | `bg-gradient-primary` |

这样配置后，你就可以直接使用 Tailwind 类名，而不需要内联样式或 CSS 变量了。
