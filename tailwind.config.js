/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        // 主题色
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          DEFAULT: '#3b82f6',
        },
        secondary: {
          50: '#ecfeff',
          100: '#cffafe',
          200: '#a5f3fc',
          300: '#67e8f9',
          400: '#22d3ee',
          500: '#06b6d4',
          600: '#0891b2',
          700: '#0e7490',
          800: '#155e75',
          900: '#164e63',
          DEFAULT: '#06b6d4',
        },
        accent: {
          50: '#faf5ff',
          100: '#f3e8ff',
          200: '#e9d5ff',
          300: '#d8b4fe',
          400: '#c084fc',
          500: '#a855f7',
          600: '#9333ea',
          700: '#7c3aed',
          800: '#6b21a8',
          900: '#581c87',
          DEFAULT: '#9333ea',
        },
        // 功能色
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#10b981',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
          DEFAULT: '#10b981',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          DEFAULT: '#f59e0b',
        },
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          DEFAULT: '#ef4444',
        },
        info: {
          50: '#ecfeff',
          100: '#cffafe',
          200: '#a5f3fc',
          300: '#67e8f9',
          400: '#22d3ee',
          500: '#06b6d4',
          600: '#0891b2',
          700: '#0e7490',
          800: '#155e75',
          900: '#164e63',
          DEFAULT: '#06b6d4',
        },
        // 深色主题颜色
        dark: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
      },
      backgroundColor: {
        'app-base': '#0f172a',
        'app-container': '#1e293b',
        'app-elevated': '#334155',
        'app-header': 'rgba(17, 24, 39, 0.8)',
        'app-sidebar': 'rgba(17, 24, 39, 0.3)',
        'app-modal': 'rgba(17, 24, 39, 0.95)',
        'app-input': 'rgba(55, 65, 81, 0.5)',
      },
      textColor: {
        'app-primary': '#f1f5f9',
        'app-secondary': '#94a3b8',
        'app-tertiary': '#64748b',
        'app-quaternary': '#475569',
        'app-placeholder': '#9ca3af',
      },
      borderColor: {
        'app-primary': '#374151',
        'app-secondary': '#4b5563',
        'app-light': 'rgba(75, 85, 99, 0.5)',
      },
      boxShadow: {
        'app-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'app-base': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'app-md': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'app-blue': '0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(147, 51, 234, 0.3)',
        'app-blue-hover': '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)',
        'app-menu': '0 4px 15px rgba(59, 130, 246, 0.25)',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6)',
        'gradient-primary-hover': 'linear-gradient(90deg, #0891b2, #2563eb, #7c3aed)',
        'gradient-button': 'linear-gradient(90deg, #06b6d4, #3b82f6, #9333ea)',
        'gradient-button-hover': 'linear-gradient(90deg, #22d3ee, #60a5fa, #a855f7)',
        'gradient-card-blue': 'linear-gradient(135deg, #06b6d4, #3b82f6)',
        'gradient-card-green': 'linear-gradient(135deg, #10b981, #059669)',
        'gradient-card-amber': 'linear-gradient(135deg, #f59e0b, #d97706)',
        'gradient-card-pink': 'linear-gradient(135deg, #ec4899, #be185d)',
        'gradient-card-purple': 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
        'gradient-app-bg': 'linear-gradient(to bottom right, #020617, #0f172a, #000000)',
      },
      animation: {
        'gradient-x': 'gradient-x 15s ease infinite',
        'gradient-y': 'gradient-y 15s ease infinite',
      },
      keyframes: {
        'gradient-y': {
          '0%, 100%': {
            'background-size': '400% 400%',
            'background-position': 'center top'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'center center'
          }
        },
        'gradient-x': {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          }
        }
      }
    },
  },
  plugins: [],
};