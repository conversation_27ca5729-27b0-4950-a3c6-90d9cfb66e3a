import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { devtools } from "zustand/middleware";

type LoginStore = {
  openLoginModal: boolean; // 登录模态框是否打开
  openRegisterModal: boolean; // 注册模态框是否打开
  isLoggedIn: boolean; // 用户是否已登录

  login: () => void; // 登录方法
  logout: () => void; // 登出方法
  toLogin: (value: boolean) => void; // 切换到登录模态框
  close: () => void; // 关闭模态框
  toRegister: () => void; // 切换到注册模态框
};

const useLoginStore = create<LoginStore>()(
  devtools(
    persist(
      (set) => ({
        openLoginModal: false, // 登录模态框是否打开
        openRegisterModal: false, // 注册模态框是否打开
        isLoggedIn: false, // 用户是否已登录

        login: () => set({ isLoggedIn: true }),
        logout: () => set({ isLoggedIn: false }),
        toLogin: () => set({ openLoginModal: true, openRegisterModal: false }),
        close: () => set({ openLoginModal: false, openRegisterModal: false }),
        toRegister: () =>
          set({ openLoginModal: false, openRegisterModal: true }),
      }),
      {
        name: "login-storage", // 存储的名称
        storage: createJSONStorage(() => localStorage), // 使用 localStorage 进行持久化存储
      }
    )
  )
);

export default useLoginStore;
