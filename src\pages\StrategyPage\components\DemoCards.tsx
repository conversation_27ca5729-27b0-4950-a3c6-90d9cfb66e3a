import React from 'react';
import { motion } from 'framer-motion';
import { Smartphone, Coffee, Shirt, Gamepad2, TrendingUp, Users, Target, Zap } from 'lucide-react';
import { useLanguage } from '../../../contexts/LanguageContext';
import { useProgressStore } from '@/contexts/ProgressContext';


const DemoCards: React.FC = () => {
  const { t } = useLanguage();
  const { startProgress} = useProgressStore();
  const demoData = [
    {
      icon: Smartphone,
      brand: '智能手机品牌',
      audience: '18-35岁科技爱好者',
      industry: '数码科技',
      budget: '100万元',
      gradient: 'from-cyan-500 to-blue-500',
      features: '5G技术、高性能处理器、专业摄影'
    },
    {
      icon: Coffee,
      brand: '精品咖啡品牌',
      audience: '25-40岁都市白领',
      industry: '食品饮料',
      budget: '50万元',
      gradient: 'from-amber-500 to-orange-500',
      features: '精选豆源、手工烘焙、便捷包装'
    },
    {
      icon: Shirt,
      brand: '时尚服装品牌',
      audience: '20-30岁时尚达人',
      industry: '服装时尚',
      budget: '80万元',
      gradient: 'from-pink-500 to-rose-500',
      features: '原创设计、环保面料、个性定制'
    },
    {
      icon: Gamepad2,
      brand: '游戏设备品牌',
      audience: '16-28岁游戏玩家',
      industry: '游戏娱乐',
      budget: '120万元',
      gradient: 'from-purple-500 to-indigo-500',
      features: '专业电竞、低延迟、人体工学'
    }
  ];

  const handleDemoClick = (demo: typeof demoData[0]) => {
    startProgress({
      brand: demo.brand,
      audience: demo.audience,
      competitors: '行业领导品牌',
      industry: demo.industry,
      budget: demo.budget,
      features: demo.features
    }, '1'); // 使用固定的taskId
  };

  return (
    <div className="h-full flex flex-col">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <h3 className="text-3xl font-bold text-white mb-4">
          {t('demo.title')}
        </h3>
        <p className="text-app-secondary text-lg">
          {t('demo.subtitle')}
        </p>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
        {demoData.map((demo, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            whileHover={{ 
              scale: 1.02, 
              y: -5,
              boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(59, 130, 246, 0.2)"
            }}
            whileTap={{ scale: 0.98 }}
            onClick={() => handleDemoClick(demo)}
            className="bg-app-container backdrop-blur-sm rounded-xl p-6 cursor-pointer hover:bg-app-elevated transition-all duration-300 border border-app-light hover:border-app-secondary group"
          >
            <div className="flex items-start space-x-4 mb-4">
              <div className={`w-12 h-12 bg-gradient-to-r ${demo.gradient} rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                <demo.icon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-bold text-white mb-2 text-lg">{demo.brand}</h4>
                <p className="text-sm text-app-secondary mb-3">{demo.audience}</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Target className="w-4 h-4 text-app-tertiary" />
                  <span className="text-sm text-app-secondary">{demo.industry}</span>
                </div>
                <span className="text-sm font-semibold text-primary">{demo.budget}</span>
              </div>
              
              <div className="flex items-start space-x-2">
                <Zap className="w-4 h-4 text-app-tertiary mt-0.5 flex-shrink-0" />
                <p className="text-xs text-app-secondary leading-relaxed">{demo.features}</p>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-app-light">
              <div className="flex items-center justify-center space-x-2 text-primary group-hover:text-primary-300 transition-colors">
                <TrendingUp className="w-4 h-4" />
                <span className="text-sm font-medium">{t('demo.generate')}</span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-8 p-6 bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl border border-primary/20 backdrop-blur-sm"
      >
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Users className="w-5 h-5 text-primary" />
            <span className="text-lg font-semibold text-white">AI策略生成演示</span>
          </div>
          <p className="text-app-primary">
            💡 点击任意品牌卡片即可体验AI智能策略生成功能，获得专业的社交媒体广告投放建议
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default DemoCards;