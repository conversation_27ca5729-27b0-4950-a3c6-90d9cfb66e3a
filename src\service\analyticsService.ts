import { api } from '../utils/request';

// 达人查询参数
export interface InfluencerParams {
  industry: string;
  budget: string;
  audience: string;
  platform: string;
  page?: number;
  limit?: number;
}

// 达人信息
export interface InfluencerResult {
  id: string;
  name: string;
  platform: string;
  followers: number;
  engagement: number;
  price: number;
  tags: string[];
  avatar?: string;
}

// 数据分析参数
export interface AnalyticsParams {
  startDate: string;
  endDate: string;
  platform?: string;
  campaignId?: string;
}

// 分析结果
export interface AnalyticsResult {
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  conversionRate: number;
  roi: number;
  dailyData: Array<{
    date: string;
    impressions: number;
    clicks: number;
    conversions: number;
  }>;
}

// 分析服务
export const analyticsService = {
  // 获取达人推荐
  getInfluencers: (params: InfluencerParams) => {
    return api.get<{
      total: number;
      items: InfluencerResult[];
    }>('/analytics/influencers', {
      params,
      showLoading: true,
    });
  },

  // 获取达人详情
  getInfluencerDetail: (id: string) => {
    return api.get<InfluencerResult>(`/analytics/influencer/${id}`);
  },

  // 获取营销数据分析
  getMarketingAnalytics: (params: AnalyticsParams) => {
    return api.get<AnalyticsResult>('/analytics/marketing', {
      params,
      showLoading: true,
    });
  },

  // 获取受众分析
  getAudienceAnalytics: (params: Omit<AnalyticsParams, 'platform'>) => {
    return api.get<{
      demographics: Array<{ age: string; percentage: number }>;
      interests: Array<{ name: string; value: number }>;
      locations: Array<{ city: string; count: number }>;
    }>('/analytics/audience', {
      params,
      showLoading: true,
    });
  },

  // 获取竞品分析
  getCompetitorAnalytics: (brand: string) => {
    return api.get<{
      competitors: Array<{ 
        name: string; 
        marketShare: number;
        sentiment: number;
        socialMentions: number;
      }>;
    }>('/analytics/competitors', {
      params: { brand },
      showLoading: true,
    });
  },
};
