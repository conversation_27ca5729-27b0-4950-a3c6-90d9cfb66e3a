/**
 * 项目统一颜色管理
 * 与 Tailwind CSS 配置保持一致，便于统一管理和主题切换
 */

// 基础颜色调色板 - 与 tailwind.config.js 完全一致
export const palette = {
  // 主色调 - Primary (蓝色系)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // DEFAULT
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    DEFAULT: '#3b82f6',
  },

  // 次要色调 - Secondary (青色系)
  secondary: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc',
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#06b6d4',  // DEFAULT
    600: '#0891b2',
    700: '#0e7490',
    800: '#155e75',
    900: '#164e63',
    DEFAULT: '#06b6d4',
  },

  // 强调色 - Accent (紫色系)
  accent: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',  // DEFAULT
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87',
    DEFAULT: '#9333ea',
  },

  // 成功色 - Success (绿色系)
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#10b981',  // DEFAULT (自定义成功色)
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    DEFAULT: '#10b981',
  },

  // 警告色 - Warning (琥珀色系)
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',  // DEFAULT
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    DEFAULT: '#f59e0b',
  },

  // 错误色 - Error (红色系)
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',  // DEFAULT
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    DEFAULT: '#ef4444',
  },

  // 信息色 - Info (青色系，与 secondary 相同)
  info: {
    50: '#ecfeff',
    100: '#cffafe',
    200: '#a5f3fc',
    300: '#67e8f9',
    400: '#22d3ee',
    500: '#06b6d4',  // DEFAULT
    600: '#0891b2',
    700: '#0e7490',
    800: '#155e75',
    900: '#164e63',
    DEFAULT: '#06b6d4',
  },

  // 深色主题色系 - Dark (石板色系)
  dark: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
    950: '#020617',
  },
} as const;

// 主题颜色定义 - 与 Tailwind CSS 配置保持一致
export const colors = {
  // 主色调 - 对应 Tailwind 的 primary
  primary: {
    ...palette.primary,
    main: palette.primary[500],     // #3b82f6
    light: palette.primary[400],    // #60a5fa
    dark: palette.primary[600],     // #2563eb
    hover: palette.primary[400],    // #60a5fa
    active: palette.primary[600],   // #2563eb
  },

  // 次要色调 - 对应 Tailwind 的 secondary
  secondary: {
    ...palette.secondary,
    main: palette.secondary[500],   // #06b6d4
    light: palette.secondary[400],  // #22d3ee
    dark: palette.secondary[600],   // #0891b2
    hover: palette.secondary[400],  // #22d3ee
    active: palette.secondary[600], // #0891b2
  },

  // 强调色 - 对应 Tailwind 的 accent
  accent: {
    ...palette.accent,
    main: palette.accent[600],      // #9333ea
    light: palette.accent[500],     // #a855f7
    dark: palette.accent[700],      // #7c3aed
    hover: palette.accent[500],     // #a855f7
    active: palette.accent[700],    // #7c3aed
  },

  // 功能色 - 对应 Tailwind 的 success
  success: {
    ...palette.success,
    main: palette.success[500],     // #10b981
    light: palette.success[400],    // #4ade80
    dark: palette.success[600],     // #16a34a
    bg: 'rgba(16, 185, 129, 0.1)',
    border: 'rgba(16, 185, 129, 0.3)',
  },

  // 警告色 - 对应 Tailwind 的 warning
  warning: {
    ...palette.warning,
    main: palette.warning[500],     // #f59e0b
    light: palette.warning[400],    // #fbbf24
    dark: palette.warning[600],     // #d97706
    bg: 'rgba(245, 158, 11, 0.1)',
    border: 'rgba(245, 158, 11, 0.3)',
  },

  // 错误色 - 对应 Tailwind 的 error
  error: {
    ...palette.error,
    main: palette.error[500],       // #ef4444
    light: palette.error[400],      // #f87171
    dark: palette.error[600],       // #dc2626
    bg: 'rgba(239, 68, 68, 0.1)',
    border: 'rgba(239, 68, 68, 0.3)',
  },

  // 信息色 - 对应 Tailwind 的 info
  info: {
    ...palette.info,
    main: palette.info[500],        // #06b6d4
    light: palette.info[400],       // #22d3ee
    dark: palette.info[600],        // #0891b2
    bg: 'rgba(6, 182, 212, 0.1)',
    border: 'rgba(6, 182, 212, 0.3)',
  },

  // 背景色系 (深色主题) - 对应 Tailwind 的 app-* 类名
  background: {
    base: palette.dark[900],        // #0f172a - 最深背景 (app-base)
    container: palette.dark[800],   // #1e293b - 容器背景 (app-container)
    elevated: palette.dark[700],    // #334155 - 悬浮背景 (app-elevated)
    layout: palette.dark[900],      // #0f172a - 布局背景
    header: 'rgba(17, 24, 39, 0.8)',     // 头部背景 (app-header)
    sidebar: 'rgba(17, 24, 39, 0.3)',    // 侧边栏背景 (app-sidebar)
    modal: 'rgba(17, 24, 39, 0.95)',     // 模态框背景 (app-modal)
    dropdown: 'rgba(31, 41, 55, 0.95)',  // 下拉菜单背景
    input: 'rgba(55, 65, 81, 0.5)',      // 输入框背景 (app-input)
    trigger: 'rgba(55, 65, 81, 0.5)',    // 触发器背景
  },

  // 文字颜色 - 对应 Tailwind 的 app-* 类名
  text: {
    primary: palette.dark[100],     // #f1f5f9 - 主要文字 (app-primary)
    secondary: palette.dark[400],   // #94a3b8 - 次要文字 (app-secondary)
    tertiary: palette.dark[500],    // #64748b - 三级文字 (app-tertiary)
    quaternary: palette.dark[600],  // #475569 - 四级文字 (app-quaternary)
    placeholder: '#9ca3af',         // #9ca3af - 占位符文字 (app-placeholder)
    white: '#ffffff',               // 纯白文字
    disabled: palette.dark[600],    // #475569 - 禁用文字
  },

  // 边框颜色 - 对应 Tailwind 的 app-* 类名
  border: {
    primary: '#374151',             // #374151 - 主边框 (app-primary)
    secondary: '#4b5563',           // #4b5563 - 次要边框 (app-secondary)
    light: 'rgba(75, 85, 99, 0.5)', // 浅色边框 (app-light)
    split: '#374151',               // #374151 - 分割线
    input: 'rgba(75, 85, 99, 0.5)', // 输入框边框
    inputHover: palette.primary[400], // #60a5fa - 输入框悬停边框
    inputActive: palette.primary[500], // #3b82f6 - 输入框激活边框
  },

  // 阴影
  shadow: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    md: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    lg: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    xl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    blue: '0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(147, 51, 234, 0.3)',
    blueHover: '0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)',
    menu: '0 4px 15px rgba(59, 130, 246, 0.25)',
    menuHover: '0 0 15px rgba(59, 130, 246, 0.3)',
  },

  // 渐变色定义
  gradients: {
    // 主要渐变
    primary: 'linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6)',
    primaryHover: 'linear-gradient(90deg, #0891b2, #2563eb, #7c3aed)',

    // 按钮渐变
    button: 'linear-gradient(90deg, #06b6d4, #3b82f6, #9333ea)',
    buttonHover: 'linear-gradient(90deg, #22d3ee, #60a5fa, #a855f7)',

    // 卡片渐变
    cardBlue: 'linear-gradient(135deg, #06b6d4, #3b82f6)',
    cardGreen: 'linear-gradient(135deg, #10b981, #059669)',
    cardAmber: 'linear-gradient(135deg, #f59e0b, #d97706)',
    cardPink: 'linear-gradient(135deg, #ec4899, #be185d)',
    cardPurple: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',

    // Tailwind 渐变类名对应
    'cyan-blue': 'from-cyan-500 to-blue-500',
    'green-emerald': 'from-green-500 to-emerald-500',
    'amber-orange': 'from-amber-500 to-orange-500',
    'pink-rose': 'from-pink-500 to-rose-500',
    'purple-indigo': 'from-purple-500 to-indigo-500',
  },

  // 菜单相关颜色 - 对应 Tailwind 的 menu-* 类名
  menu: {
    // 深色菜单
    dark: {
      bg: 'transparent',
      itemBg: 'transparent',
      itemSelectedBg: 'transparent',
      itemHoverBg: 'rgba(55, 65, 81, 0.5)',
      subMenuItemBg: 'rgba(31, 41, 55, 0.5)',
      itemColor: palette.dark[400],       // #94a3b8
      itemSelectedColor: '#ffffff',
      itemHoverColor: palette.dark[100],  // #f1f5f9
      selectedGradient: 'linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6)',
      selectedGradientHover: 'linear-gradient(90deg, #0891b2, #2563eb, #7c3aed)',
      subSelectedGradient: 'linear-gradient(90deg, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8))',
    },
  },

  // 状态颜色
  state: {
    hover: 'rgba(55, 65, 81, 0.5)',
    active: 'rgba(75, 85, 99, 0.7)',
    focus: palette.primary[500],
    disabled: 'rgba(107, 114, 128, 0.5)',
    loading: palette.primary[500],
  },

  // 滚动条颜色
  scrollbar: {
    track: 'rgba(31, 41, 55, 0.3)',
    thumb: 'rgba(75, 85, 99, 0.5)',
    thumbHover: 'rgba(107, 114, 128, 0.7)',
  },
} as const;

// 与 Tailwind CSS 完全对应的颜色映射
export const tailwindColors = {
  // 主题色 - 对应 bg-primary, text-primary 等
  primary: palette.primary,
  secondary: palette.secondary,
  accent: palette.accent,

  // 功能色 - 对应 bg-success, text-success 等
  success: palette.success,
  warning: palette.warning,
  error: palette.error,
  info: palette.info,

  // 深色主题 - 对应 bg-dark-*, text-dark-* 等
  dark: palette.dark,

  // 应用专用背景色 - 对应 bg-app-* 类名
  'app-base': colors.background.base,
  'app-container': colors.background.container,
  'app-elevated': colors.background.elevated,
  'app-header': colors.background.header,
  'app-sidebar': colors.background.sidebar,
  'app-modal': colors.background.modal,
  'app-input': colors.background.input,

  // 应用专用文字色 - 对应 text-app-* 类名
  'app-primary': colors.text.primary,
  'app-secondary': colors.text.secondary,
  'app-tertiary': colors.text.tertiary,
  'app-quaternary': colors.text.quaternary,
  'app-placeholder': colors.text.placeholder,

  // 应用专用边框色 - 对应 border-app-* 类名
  'app-border-primary': colors.border.primary,
  'app-border-secondary': colors.border.secondary,
  'app-border-light': colors.border.light,

  // 渐变色 - 对应 bg-gradient-* 类名
  'gradient-primary': colors.gradients.primary,
  'gradient-primary-hover': colors.gradients.primaryHover,
  'gradient-button': colors.gradients.button,
  'gradient-button-hover': colors.gradients.buttonHover,
  'gradient-card-blue': colors.gradients.cardBlue,
  'gradient-card-green': colors.gradients.cardGreen,
  'gradient-card-amber': colors.gradients.cardAmber,
  'gradient-card-pink': colors.gradients.cardPink,
  'gradient-card-purple': colors.gradients.cardPurple,
  'gradient-app-bg': 'linear-gradient(to bottom right, #020617, #0f172a, #000000)',

  // 阴影 - 对应 shadow-app-* 类名
  'shadow-app-sm': colors.shadow.sm,
  'shadow-app-base': colors.shadow.base,
  'shadow-app-md': colors.shadow.md,
  'shadow-app-blue': colors.shadow.blue,
  'shadow-app-blue-hover': colors.shadow.blueHover,
  'shadow-app-menu': colors.shadow.menu,
} as const;

// 导出类型定义
export type PaletteColor = keyof typeof palette;
export type ThemeColor = keyof typeof colors;
export type TailwindColor = keyof typeof tailwindColors;

// 颜色工具函数
export const colorUtils = {
  /**
   * 获取颜色的 rgba 格式
   * @param color 颜色值
   * @param alpha 透明度 (0-1)
   */
  rgba: (color: string, alpha: number): string => {
    // 如果已经是 rgba 格式，直接返回
    if (color.startsWith('rgba')) return color;

    // 如果是 rgb 格式，转换为 rgba
    if (color.startsWith('rgb')) {
      return color.replace('rgb', 'rgba').replace(')', `, ${alpha})`);
    }

    // 如果是十六进制格式，转换为 rgba
    if (color.startsWith('#')) {
      const hex = color.replace('#', '');
      const r = parseInt(hex.substring(0, 2), 16);
      const g = parseInt(hex.substring(2, 4), 16);
      const b = parseInt(hex.substring(4, 6), 16);
      return `rgba(${r}, ${g}, ${b}, ${alpha})`;
    }

    return color;
  },

  /**
   * 获取渐变色的 CSS 值
   * @param gradientKey 渐变色键名
   */
  getGradient: (gradientKey: keyof typeof colors.gradients): string => {
    return colors.gradients[gradientKey];
  },

  /**
   * 获取阴影的 CSS 值
   * @param shadowKey 阴影键名
   */
  getShadow: (shadowKey: keyof typeof colors.shadow): string => {
    return colors.shadow[shadowKey];
  },
};
