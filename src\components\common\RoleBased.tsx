import React, { ReactNode } from 'react';
import { isAdmin, isUser, isAdminFeaturesEnabled, isAdvancedAnalyticsEnabled } from '../../utils/env';

interface RoleBasedProps {
  children: ReactNode;
  /**
   * 需要的最低权限级别，默认为 'guest'
   */
  minRole?: 'admin' | 'user' | 'guest';
  /**
   * 是否要求管理员功能启用，默认为 false
   */
  requireAdminFeatures?: boolean;
  /**
   * 是否要求高级分析功能启用，默认为 false
   */
  requireAdvancedAnalytics?: boolean;
  /**
   * 当权限不足时显示的内容，默认为 null
   */
  fallback?: ReactNode;
}

/**
 * 根据用户角色条件性地渲染内容的组件
 */
const RoleBased: React.FC<RoleBasedProps> = ({
  children,
  minRole = 'guest',
  requireAdminFeatures = false,
  requireAdvancedAnalytics = false,
  fallback = null,
}) => {
  // 检查用户角色是否满足最低要求
  const hasRequiredRole = () => {
    switch (minRole) {
      case 'admin':
        return isAdmin();
      case 'user':
        return isUser();
      case 'guest':
        return true;
      default:
        return true;
    }
  };

  // 检查是否满足其他功能要求
  const meetsRequirements = () => {
    if (requireAdminFeatures && !isAdminFeaturesEnabled()) {
      return false;
    }
    
    if (requireAdvancedAnalytics && !isAdvancedAnalyticsEnabled()) {
      return false;
    }
    
    return true;
  };

  // 检查所有条件
  const shouldRender = hasRequiredRole() && meetsRequirements();

  return shouldRender ? <>{children}</> : <>{fallback}</>;
};

export default RoleBased;
