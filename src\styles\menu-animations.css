/* Ant Design Menu 自定义动画样式 */

/* 菜单项基础样式 */
.ant-menu-dark .ant-menu-item,
.ant-menu-dark .ant-menu-submenu-title {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
  margin: 4px 8px;
  overflow: hidden;
}

/* 菜单项悬停效果 */
.ant-menu-dark .ant-menu-item:hover,
.ant-menu-dark .ant-menu-submenu-title:hover {
  background: var(--state-hover) !important;
  color: var(--text-primary) !important;
  transform: scale(1.02);
  box-shadow: var(--shadow-menu-hover);
}

/* 菜单项选中状态 - 渐变背景 */
.ant-menu-dark .ant-menu-item-selected {
  background: var(--gradient-primary) !important;
  color: var(--text-white) !important;
  box-shadow: var(--shadow-menu);
  transform: scale(1.02);
}

/* 菜单项选中状态悬停 */
.ant-menu-dark .ant-menu-item-selected:hover {
  background: var(--gradient-primary-hover) !important;
  box-shadow: var(--shadow-blue-hover);
}

/* 菜单项图标动画 */
.ant-menu-dark .ant-menu-item .anticon,
.ant-menu-dark .ant-menu-submenu-title .anticon {
  transition: all 0.3s ease;
}

.ant-menu-dark .ant-menu-item:hover .anticon,
.ant-menu-dark .ant-menu-submenu-title:hover .anticon {
  transform: scale(1.1);
}

/* 子菜单项样式 */
.ant-menu-dark .ant-menu-sub .ant-menu-item {
  background: var(--menu-dark-submenu-item-bg) !important;
  border-radius: 6px;
  margin: 2px 4px;
  padding-left: 48px !important;
}

.ant-menu-dark .ant-menu-sub .ant-menu-item:hover {
  background: var(--state-active) !important;
  transform: translateX(4px) scale(1.02);
}

.ant-menu-dark .ant-menu-sub .ant-menu-item-selected {
  background: linear-gradient(90deg, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8)) !important;
}

/* 子菜单展开动画 */
.ant-menu-dark .ant-menu-submenu .ant-menu-sub {
  background: transparent !important;
}

/* 菜单项内容布局 */
.ant-menu-dark .ant-menu-item .ant-menu-title-content,
.ant-menu-dark .ant-menu-submenu-title .ant-menu-title-content {
  transition: all 0.3s ease;
}



/* 菜单项点击动画 */
.ant-menu-dark .ant-menu-item:active,
.ant-menu-dark .ant-menu-submenu-title:active {
  transform: scale(0.98);
}

/* 子菜单箭头动画 */
.ant-menu-dark .ant-menu-submenu-arrow {
  transition: transform 0.3s ease;
}

.ant-menu-dark .ant-menu-submenu-open > .ant-menu-submenu-title .ant-menu-submenu-arrow {
  transform: rotate(90deg);
}

/* 历史记录项特殊样式 */
.ant-menu-dark .ant-menu-item[data-menu-id^="history-"] {
  padding: 8px 16px !important;
  height: auto !important;
  line-height: 1.4;
}

.ant-menu-dark .ant-menu-item[data-menu-id^="history-"] .ant-menu-title-content {
  display: block;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-menu-dark .ant-menu-item,
  .ant-menu-dark .ant-menu-submenu-title {
    margin: 2px 4px;
    padding: 8px 12px;
  }
}

/* 滚动条样式 */
.ant-menu-dark::-webkit-scrollbar {
  width: 4px;
}

.ant-menu-dark::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 2px;
}

.ant-menu-dark::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 2px;
}

.ant-menu-dark::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 菜单项加载动画 */
@keyframes menuItemFadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.ant-menu-dark .ant-menu-item {
  animation: menuItemFadeIn 0.3s ease-out;
}

/* 渐变动画效果 */
.ant-menu-dark .ant-menu-item-selected {
  background-size: 200% 100%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
