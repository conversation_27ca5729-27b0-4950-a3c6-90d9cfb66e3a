#!/usr/bin/env node
// 依赖分析工具 - ESM 版本
// 运行方式: node dependency-analyzer.mjs

import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

// ES Module 环境下获取 __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 要分析的第三方库列表
const libraries = [
  "axios",
  "react-markdown",
  "rehype-raw",
  "remark-gfm",
  "react-syntax-highlighter",
  "antd",
  "@ant-design/icons",
  "framer-motion",
  "react-router-dom",
  "highlight.js",
];

// 源代码目录
const srcDir = path.resolve(__dirname, "src");

// 分析结果
const results = {};

// 初始化结果对象
libraries.forEach((lib) => {
  results[lib] = [];
});

// 递归查找文件
function findFiles(dir) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      findFiles(fullPath);
    } else if (/\.(ts|tsx|js|jsx)$/.test(file)) {
      analyzeFile(fullPath);
    }
  });
}

// 分析单个文件
function analyzeFile(filePath) {
  const content = fs.readFileSync(filePath, "utf-8");
  const relativePath = path.relative(srcDir, filePath);

  libraries.forEach((lib) => {
    // 匹配 import 语句
    const importRegex = new RegExp(
      `import\\s+(?:.+\\s+from\\s+)?['"]${lib}(?:/[^'"]*)?['"]`,
      "g"
    );
    if (importRegex.test(content)) {
      results[lib].push(relativePath);
    }
  });
}

// 开始分析
console.log("开始分析依赖关系...");
findFiles(srcDir);

// 打印结果
console.log("\n===== 依赖分析结果 =====\n");
libraries.forEach((lib) => {
  if (results[lib].length > 0) {
    console.log(`\n${lib} 被以下文件引用 (${results[lib].length} 个文件):`);
    results[lib].forEach((file) => {
      console.log(`  - ${file}`);
    });
  } else {
    console.log(`\n${lib} 没有被任何文件直接引用`);
  }
});

console.log("\n\n=== 详细的包大小分析 ===");
console.log(
  "运行 npm run build:analyze 并打开 dist/stats.html 查看详细的包大小分析"
);
