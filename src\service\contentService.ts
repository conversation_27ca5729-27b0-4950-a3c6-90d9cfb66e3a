import { api } from '../utils/request';

// 内容生成参数
export interface ContentGenerationParams {
  topic: string;
  platform: string;
  style: string;
  length: number;
}

// 内容结果
export interface ContentResult {
  id?: string;
  title: string;
  content: string;
  hashtags: string[];
  suggestions: string[];
  createdAt?: string;
}

// 情感分析参数
export interface SentimentAnalysisParams {
  content: string;
  platform?: string;
}

// 情感分析结果
export interface SentimentResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  score: number;
  keywords: string[];
  suggestions: string[];
}

// 内容相关API服务
export const contentService = {
  // 生成内容
  generateContent: (params: ContentGenerationParams) => {
    return api.post<ContentResult>('/content/generate', params, {
      showLoading: true,
      showSuccess: true,
    });
  },

  // 内容情感分析
  analyzeSentiment: (params: SentimentAnalysisParams) => {
    return api.post<SentimentResult>('/content/sentiment', params, {
      showLoading: true,
    });
  },

  // 获取内容历史
  getContentHistory: (page = 1, limit = 10) => {
    return api.get<{
      total: number;
      items: ContentResult[];
    }>('/content/history', {
      params: { page, limit },
    });
  },

  // 删除内容
  deleteContent: (id: string) => {
    return api.delete(`/content/${id}`, {
      showSuccess: true,
    });
  },

  // 保存内容
  saveContent: (data: ContentResult) => {
    return api.post<{ id: string }>('/content/save', data, {
      showSuccess: true,
    });
  },
};
