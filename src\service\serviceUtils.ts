import { api } from '../utils/request';
import { AxiosRequestConfig, AxiosResponse } from 'axios';

// 拦截器类型
export type RequestInterceptor = (config: AxiosRequestConfig) => AxiosRequestConfig;
export type ResponseInterceptor = (response: AxiosResponse) => AxiosResponse;
export type ErrorInterceptor = (error: unknown) => unknown;

/**
 * 创建自定义API服务实例
 * 可以为特定模块添加专门的请求/响应拦截器
 */
export const createServiceInstance = (options?: {
  baseURL?: string;
  requestInterceptors?: RequestInterceptor[];
  responseInterceptors?: ResponseInterceptor[];
  errorInterceptors?: ErrorInterceptor[];
}) => {
  // 基础服务实例，这里复用现有的api实例
  const serviceInstance = {
    // GET请求
    get: <T = unknown>(url: string, config?: AxiosRequestConfig) => {
      return api.get<T>(url, {
        ...config,
        baseURL: options?.baseURL,
      });
    },

    // POST请求
    post: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
      return api.post<T>(url, data, {
        ...config,
        baseURL: options?.baseURL,
      });
    },

    // PUT请求
    put: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
      return api.put<T>(url, data, {
        ...config,
        baseURL: options?.baseURL,
      });
    },

    // DELETE请求
    delete: <T = unknown>(url: string, config?: AxiosRequestConfig) => {
      return api.delete<T>(url, {
        ...config,
        baseURL: options?.baseURL,
      });
    },

    // PATCH请求
    patch: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) => {
      return api.patch<T>(url, data, {
        ...config,
        baseURL: options?.baseURL,
      });
    },
  };

  return serviceInstance;
};

/**
 * 创建模拟API服务实例
 * 用于开发和测试时，可以不依赖真实后端
 */
export const createMockServiceInstance = <T extends Record<string, unknown>>(
  mockData: T,
  delay = 500
) => {
  const mockInstance = {} as {
    [K in keyof T]: (...args: unknown[]) => Promise<T[K]>;
  };

  // 为每个mock数据创建对应的方法
  Object.keys(mockData).forEach((key) => {
    mockInstance[key as keyof T] = (..._: unknown[]) => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(mockData[key]);
        }, delay);
      });
    };
  });

  return mockInstance;
};

// 导出默认实例
export default {
  createServiceInstance,
  createMockServiceInstance,
};
