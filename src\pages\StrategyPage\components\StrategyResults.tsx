import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Target,
  PenTool,
  Users,
  ChevronRight,
  Copy,
  Download,
  Share,
  CheckCircle,
  Check,
} from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import MarkdownReader from "@/components/common/ReadMarkDown";
import { useStrategyData, useProgressStore } from "@/contexts/ProgressContext";
import StrategyCard from "./StrategyCard";

const StrategyResults: React.FC = () => {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const [copied, setCopied] = useState(false);
  const { resetState } = useProgressStore();
  const data = useStrategyData();
  
  const steps = [
    {
      id: 1,
      title: t("results.core"),
      icon: Target,
      content: data?.[0]?.coreStrategy || "策略核心内容将在这里显示",
      color: "from-cyan-500 to-blue-500",
    },
    {
      id: 2,
      title: t("results.content"),
      icon: PenTool,
      content: data?.[0]?.contentPlan || "内容策略将在这里显示",
      color: "from-green-500 to-emerald-500",
    },
    {
      id: 3,
      title: t("results.influencer"),
      icon: Users,
      content: data?.[0]?.influencerStrategy || "达人策略将在这里显示",
      color: "from-purple-500 to-pink-500",
    },
  ];

  const currentStepData = steps.find((step) => step.id === currentStep);

  // 复制功能
  const handleCopy = async () => {
    try {
      const contentToCopy = currentStepData?.content || "";

      if (navigator.clipboard && window.isSecureContext) {
        // 使用现代 Clipboard API
        await navigator.clipboard.writeText(contentToCopy);
      } else {
        // 降级方案：使用传统方法
        const textArea = document.createElement("textarea");
        textArea.value = contentToCopy;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        document.execCommand("copy");
        textArea.remove();
      }

      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // 2秒后重置状态
    } catch (err) {
      console.error("复制失败:", err);
      // 可以在这里添加错误提示
      alert("复制失败，请手动选择文本复制");
    }
  };

  // render
  const renderStepContent = () => {
    if (!currentStepData) return null;

    switch (currentStepData.id) {
      case 1:
        return data?.map((_item, index) => <StrategyCard key={index} />);

      default:
        return (
          <div className="prose prose-invert max-w-none">
            <MarkdownReader content={currentStepData.content} />
          </div>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      className="h-full flex flex-col"
    >
      <div className="flex items-center justify-between mb-8">
        <div>
          <h3 className="text-3xl font-bold text-white mb-2">策略生成结果</h3>
          <p className="text-gray-400">
            基于您的品牌信息生成的专业广告投放策略
          </p>
        </div>
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={resetState}
          className="p-3 hover:bg-gray-800/50 rounded-xl transition-colors border border-gray-700/50 hover:border-gray-600/50"
        >
          <X className="w-6 h-6 text-gray-400" />
        </motion.button>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center space-x-4 mb-8">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <motion.div
              whileHover={{
                scale: 1.05,
                boxShadow:
                  currentStep !== step.id
                    ? "0 0 15px rgba(59, 130, 246, 0.3)"
                    : undefined,
              }}
              className={`flex items-center space-x-3 px-4 py-3 rounded-xl cursor-pointer transition-all duration-300 ${
                currentStep >= step.id
                  ? `bg-gradient-to-r ${step.color} text-white shadow-lg shadow-blue-500/25`
                  : "bg-gray-800/30 text-gray-400 hover:bg-gray-700/30 border border-gray-700/50 hover:border-gray-600/50"
              }`}
              onClick={() => setCurrentStep(step.id)}
            >
              <step.icon className="w-5 h-5" />
              <div>
                <div className="text-sm font-medium">{step.title}</div>
                <div className="text-xs opacity-75">第{step.id}步</div>
              </div>
              {currentStep >= step.id && <CheckCircle className="w-4 h-4" />}
            </motion.div>
            {index < steps.length - 1 && (
              <ChevronRight className="w-5 h-5 text-gray-600" />
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-hidden">
        <AnimatePresence mode="wait">
          {currentStepData && (
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="h-full flex flex-col"
            >
              <div
                className={`bg-gradient-to-r ${currentStepData.color} p-6 rounded-xl mb-6 shadow-lg`}
              >
                <div className="flex items-center space-x-4">
                  <currentStepData.icon className="w-8 h-8 text-white" />
                  <div>
                    <h4 className="text-xl font-bold text-white">
                      {currentStepData.title}
                    </h4>
                    <p className="text-white/80 text-sm">
                      专业AI分析生成的策略建议
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex-1 bg-gray-800/30 backdrop-blur-sm rounded-xl p-6 overflow-y-auto border border-gray-700/50">
                {renderStepContent()}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3 mt-6 ml-2 ">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 border ${
                    copied
                      ? "bg-green-500/20 border-green-500/50 text-green-400"
                      : "bg-gray-800/50 hover:bg-gray-700/50 border-gray-700/50 hover:border-gray-600/50 text-gray-300"
                  }`}
                  onClick={handleCopy}
                  disabled={copied}
                >
                  {copied ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                  <span className="text-sm">
                    {copied ? "已复制" : "复制内容"}
                  </span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg transition-colors border border-gray-700/50 hover:border-gray-600/50"
                >
                  <Download className="w-4 h-4 text-gray-300" />
                  <span className="text-sm text-gray-300">导出PDF</span>
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 rounded-lg transition-colors border border-gray-700/50 hover:border-gray-600/50"
                >
                  <Share className="w-4 h-4 text-gray-300" />
                  <span className="text-sm text-gray-300">分享策略</span>
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center mt-8">
        {currentStep > 1 && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setCurrentStep(currentStep - 1)}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-800/50 hover:bg-gray-700/50 text-white font-medium rounded-lg transition-all duration-200 border border-gray-700/50 hover:border-gray-600/50"
          >
            <ChevronRight className="w-4 h-4 rotate-180" />
            <span>上一步</span>
          </motion.button>
        )}

        <div className="flex-1"></div>

        {currentStep < steps.length ? (
          <motion.button
            whileHover={{
              scale: 1.02,
              boxShadow:
                "0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)",
            }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setCurrentStep(currentStep + 1)}
            className="bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 hover:from-cyan-400 hover:via-blue-400 hover:to-purple-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center space-x-2 shadow-lg shadow-blue-500/25 relative overflow-hidden group"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">
              {currentStep === 1
                ? t("button.viewContent")
                : t("button.viewInfluencer")}
            </span>
            <ChevronRight className="w-4 h-4 relative z-10" />
          </motion.button>
        ) : (
          <motion.button
            whileHover={{
              scale: 1.02,
              boxShadow:
                "0 0 25px rgba(34, 197, 94, 0.6), 0 0 50px rgba(16, 185, 129, 0.4)",
            }}
            whileTap={{ scale: 0.98 }}
            onClick={resetState}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-400 hover:to-emerald-400 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg shadow-green-500/25 relative overflow-hidden group"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">{t("button.newStrategy")}</span>
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default StrategyResults;
