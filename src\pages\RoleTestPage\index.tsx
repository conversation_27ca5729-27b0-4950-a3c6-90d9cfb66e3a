import React from 'react';
import { Card, Typography, Alert, Divider } from 'antd';
import RoleBased from '../../components/common/RoleBased';
import * as env from '../../utils/env';

const { Title, Text, Paragraph } = Typography;

const RoleTestPage: React.FC = () => {
  const userRole = env.getUserRole();
  const isAdminFeatures = env.isAdminFeaturesEnabled();
  const isAdvancedAnalytics = env.isAdvancedAnalyticsEnabled();

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <Title level={2}>角色权限测试页面</Title>
      
      <Card className="mb-6">
        <Title level={4}>当前环境配置</Title>
        <Paragraph>
          <Text strong>应用标题：</Text> {env.getAppTitle()}
        </Paragraph>
        <Paragraph>
          <Text strong>应用版本：</Text> {env.getAppVersion()}
        </Paragraph>
        <Paragraph>
          <Text strong>当前用户角色：</Text> {userRole}
        </Paragraph>
        <Paragraph>
          <Text strong>管理员功能：</Text> {isAdminFeatures ? '启用' : '禁用'}
        </Paragraph>
        <Paragraph>
          <Text strong>高级分析功能：</Text> {isAdvancedAnalytics ? '启用' : '禁用'}
        </Paragraph>
      </Card>

      <Divider />
      
      <Title level={3}>基于角色的内容展示</Title>

      {/* 管理员内容 */}
      <RoleBased
        minRole="admin"
        fallback={
          <Alert
            type="warning"
            message="管理员专属内容"
            description="您需要管理员权限才能查看此内容。请使用 npm run dev:admin 以管理员权限重新启动应用。"
            className="mb-4"
          />
        }
      >
        <Card className="mb-4 border-2 border-blue-500">
          <Title level={4}>管理员控制面板</Title>
          <Paragraph>
            这是只有管理员才能看到的内容。如果您能看到这段内容，说明您当前以管理员权限运行应用。
          </Paragraph>
        </Card>
      </RoleBased>

      {/* 普通用户内容 */}
      <RoleBased
        minRole="user"
        fallback={
          <Alert
            type="info"
            message="用户专属内容"
            description="您需要至少普通用户权限才能查看此内容。请使用 npm run dev:user 或 npm run dev:admin 重新启动应用。"
            className="mb-4"
          />
        }
      >
        <Card className="mb-4 border-2 border-green-500">
          <Title level={4}>用户功能区</Title>
          <Paragraph>
            这是普通用户和管理员才能看到的内容。如果您能看到这段内容，说明您当前以用户或管理员权限运行应用。
          </Paragraph>
        </Card>
      </RoleBased>

      {/* 高级分析功能 */}
      <RoleBased
        requireAdvancedAnalytics={true}
        fallback={
          <Alert
            type="info"
            message="高级分析功能"
            description="这里需要启用高级分析功能。请使用 npm run dev:user 或 npm run dev:admin 重新启动应用。"
            className="mb-4"
          />
        }
      >
        <Card className="mb-4 border-2 border-purple-500">
          <Title level={4}>数据分析仪表盘</Title>
          <Paragraph>
            这里展示了高级分析功能。如果您能看到这段内容，说明当前环境已启用高级分析功能。
          </Paragraph>
        </Card>
      </RoleBased>

      {/* 管理员功能 */}
      <RoleBased
        requireAdminFeatures={true}
        fallback={
          <Alert
            type="info"
            message="管理员功能"
            description="这里需要启用管理员功能。请使用 npm run dev:admin 重新启动应用。"
            className="mb-4"
          />
        }
      >
        <Card className="mb-4 border-2 border-yellow-500">
          <Title level={4}>系统管理功能</Title>
          <Paragraph>
            这里展示了管理员特有功能。如果您能看到这段内容，说明当前环境已启用管理员功能。
          </Paragraph>
        </Card>
      </RoleBased>

      {/* 访客内容 */}
      <Card className="mb-4">
        <Title level={4}>公共内容</Title>
        <Paragraph>
          这是所有人都能看到的内容，无论权限如何。
        </Paragraph>
      </Card>
    </div>
  );
};

export default RoleTestPage;
