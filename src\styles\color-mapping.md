# 颜色对象与 Tailwind CSS 类名对应关系

本文档展示了 TypeScript 颜色对象与 Tailwind CSS 类名的完整对应关系。

## 🎨 主题色对应关系

### Primary (主色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.primary.main` | `bg-primary` / `text-primary` | `#3b82f6` | 主色默认值 |
| `colors.primary[50]` | `bg-primary-50` / `text-primary-50` | `#eff6ff` | 最浅色 |
| `colors.primary[100]` | `bg-primary-100` / `text-primary-100` | `#dbeafe` | 很浅色 |
| `colors.primary[400]` | `bg-primary-400` / `text-primary-400` | `#60a5fa` | 浅色/悬停色 |
| `colors.primary[500]` | `bg-primary-500` / `text-primary-500` | `#3b82f6` | 默认色 |
| `colors.primary[600]` | `bg-primary-600` / `text-primary-600` | `#2563eb` | 深色/激活色 |
| `colors.primary[900]` | `bg-primary-900` / `text-primary-900` | `#1e3a8a` | 最深色 |

### Secondary (次要色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.secondary.main` | `bg-secondary` / `text-secondary` | `#06b6d4` | 次要色默认值 |
| `colors.secondary[400]` | `bg-secondary-400` / `text-secondary-400` | `#22d3ee` | 浅色 |
| `colors.secondary[500]` | `bg-secondary-500` / `text-secondary-500` | `#06b6d4` | 默认色 |
| `colors.secondary[600]` | `bg-secondary-600` / `text-secondary-600` | `#0891b2` | 深色 |

### Accent (强调色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.accent.main` | `bg-accent` / `text-accent` | `#9333ea` | 强调色默认值 |
| `colors.accent[500]` | `bg-accent-500` / `text-accent-500` | `#a855f7` | 浅色 |
| `colors.accent[600]` | `bg-accent-600` / `text-accent-600` | `#9333ea` | 默认色 |
| `colors.accent[700]` | `bg-accent-700` / `text-accent-700` | `#7c3aed` | 深色 |

## 🚦 功能色对应关系

### Success (成功色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.success.main` | `bg-success` / `text-success` | `#10b981` | 成功色默认值 |
| `colors.success.bg` | `bg-success-100` | `rgba(16, 185, 129, 0.1)` | 成功背景色 |
| `colors.success.border` | `border-success-300` | `rgba(16, 185, 129, 0.3)` | 成功边框色 |

### Warning (警告色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.warning.main` | `bg-warning` / `text-warning` | `#f59e0b` | 警告色默认值 |
| `colors.warning.bg` | `bg-warning-100` | `rgba(245, 158, 11, 0.1)` | 警告背景色 |
| `colors.warning.border` | `border-warning-300` | `rgba(245, 158, 11, 0.3)` | 警告边框色 |

### Error (错误色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.error.main` | `bg-error` / `text-error` | `#ef4444` | 错误色默认值 |
| `colors.error.bg` | `bg-error-100` | `rgba(239, 68, 68, 0.1)` | 错误背景色 |
| `colors.error.border` | `border-error-300` | `rgba(239, 68, 68, 0.3)` | 错误边框色 |

### Info (信息色)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.info.main` | `bg-info` / `text-info` | `#06b6d4` | 信息色默认值 |
| `colors.info.bg` | `bg-info-100` | `rgba(6, 182, 212, 0.1)` | 信息背景色 |
| `colors.info.border` | `border-info-300` | `rgba(6, 182, 212, 0.3)` | 信息边框色 |

## 🌙 深色主题对应关系

### Dark (深色主题)
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `palette.dark[50]` | `bg-dark-50` / `text-dark-50` | `#f8fafc` | 最浅色 |
| `palette.dark[100]` | `bg-dark-100` / `text-dark-100` | `#f1f5f9` | 主要文字色 |
| `palette.dark[400]` | `bg-dark-400` / `text-dark-400` | `#94a3b8` | 次要文字色 |
| `palette.dark[500]` | `bg-dark-500` / `text-dark-500` | `#64748b` | 三级文字色 |
| `palette.dark[600]` | `bg-dark-600` / `text-dark-600` | `#475569` | 四级文字色 |
| `palette.dark[700]` | `bg-dark-700` / `text-dark-700` | `#334155` | 悬浮背景 |
| `palette.dark[800]` | `bg-dark-800` / `text-dark-800` | `#1e293b` | 容器背景 |
| `palette.dark[900]` | `bg-dark-900` / `text-dark-900` | `#0f172a` | 基础背景 |
| `palette.dark[950]` | `bg-dark-950` / `text-dark-950` | `#020617` | 最深背景 |

## 🏠 应用专用颜色对应关系

### 背景色
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.background.base` | `bg-app-base` | `#0f172a` | 应用基础背景 |
| `colors.background.container` | `bg-app-container` | `#1e293b` | 容器背景 |
| `colors.background.elevated` | `bg-app-elevated` | `#334155` | 悬浮背景 |
| `colors.background.header` | `bg-app-header` | `rgba(17, 24, 39, 0.8)` | 头部背景 |
| `colors.background.sidebar` | `bg-app-sidebar` | `rgba(17, 24, 39, 0.3)` | 侧边栏背景 |
| `colors.background.modal` | `bg-app-modal` | `rgba(17, 24, 39, 0.95)` | 模态框背景 |
| `colors.background.input` | `bg-app-input` | `rgba(55, 65, 81, 0.5)` | 输入框背景 |

### 文字色
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.text.primary` | `text-app-primary` | `#f1f5f9` | 主要文字 |
| `colors.text.secondary` | `text-app-secondary` | `#94a3b8` | 次要文字 |
| `colors.text.tertiary` | `text-app-tertiary` | `#64748b` | 三级文字 |
| `colors.text.quaternary` | `text-app-quaternary` | `#475569` | 四级文字 |
| `colors.text.placeholder` | `text-app-placeholder` | `#9ca3af` | 占位符文字 |

### 边框色
| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.border.primary` | `border-app-primary` | `#374151` | 主边框 |
| `colors.border.secondary` | `border-app-secondary` | `#4b5563` | 次要边框 |
| `colors.border.light` | `border-app-light` | `rgba(75, 85, 99, 0.5)` | 浅色边框 |

## 🌈 渐变色对应关系

| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.gradients.primary` | `bg-gradient-primary` | `linear-gradient(90deg, #06b6d4, #3b82f6, #8b5cf6)` | 主要渐变 |
| `colors.gradients.primaryHover` | `bg-gradient-primary-hover` | `linear-gradient(90deg, #0891b2, #2563eb, #7c3aed)` | 主要渐变悬停 |
| `colors.gradients.button` | `bg-gradient-button` | `linear-gradient(90deg, #06b6d4, #3b82f6, #9333ea)` | 按钮渐变 |
| `colors.gradients.buttonHover` | `bg-gradient-button-hover` | `linear-gradient(90deg, #22d3ee, #60a5fa, #a855f7)` | 按钮渐变悬停 |
| `colors.gradients.cardBlue` | `bg-gradient-card-blue` | `linear-gradient(135deg, #06b6d4, #3b82f6)` | 蓝色卡片渐变 |
| `colors.gradients.cardGreen` | `bg-gradient-card-green` | `linear-gradient(135deg, #10b981, #059669)` | 绿色卡片渐变 |
| `colors.gradients.cardAmber` | `bg-gradient-card-amber` | `linear-gradient(135deg, #f59e0b, #d97706)` | 琥珀色卡片渐变 |
| `colors.gradients.cardPink` | `bg-gradient-card-pink` | `linear-gradient(135deg, #ec4899, #be185d)` | 粉色卡片渐变 |
| `colors.gradients.cardPurple` | `bg-gradient-card-purple` | `linear-gradient(135deg, #8b5cf6, #7c3aed)` | 紫色卡片渐变 |
| - | `bg-gradient-app-bg` | `linear-gradient(to bottom right, #020617, #0f172a, #000000)` | 应用背景渐变 |

## 🌟 阴影对应关系

| 颜色对象 | Tailwind 类名 | 颜色值 | 说明 |
|---------|---------------|--------|------|
| `colors.shadow.sm` | `shadow-app-sm` | `0 1px 2px 0 rgba(0, 0, 0, 0.05)` | 小阴影 |
| `colors.shadow.base` | `shadow-app-base` | `0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)` | 基础阴影 |
| `colors.shadow.md` | `shadow-app-md` | `0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)` | 中等阴影 |
| `colors.shadow.blue` | `shadow-app-blue` | `0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(147, 51, 234, 0.3)` | 蓝色阴影 |
| `colors.shadow.blueHover` | `shadow-app-blue-hover` | `0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)` | 蓝色阴影悬停 |
| `colors.shadow.menu` | `shadow-app-menu` | `0 4px 15px rgba(59, 130, 246, 0.25)` | 菜单阴影 |

## 📝 使用示例

### 在 TypeScript 中使用颜色对象
```typescript
import { colors, palette } from '../styles/colors';

// 使用主题色
const buttonStyle = {
  backgroundColor: colors.primary.main,
  color: colors.text.white,
};

// 使用调色板
const cardStyle = {
  backgroundColor: palette.dark[800],
  color: palette.dark[100],
};
```

### 在 HTML/JSX 中使用 Tailwind 类名
```html
<!-- 使用主题色 -->
<button class="bg-primary text-white">主要按钮</button>

<!-- 使用应用专用颜色 -->
<div class="bg-app-container text-app-primary border border-app-light">
  容器内容
</div>

<!-- 使用渐变 -->
<div class="bg-gradient-button text-white">渐变按钮</div>

<!-- 使用阴影 -->
<div class="shadow-app-blue">带蓝色阴影的元素</div>
```

## 🔄 迁移指南

### 从内联样式迁移到 Tailwind 类名
```html
<!-- 旧方式 -->
<div style="background-color: #1e293b; color: #f1f5f9;">内容</div>

<!-- 新方式 -->
<div class="bg-app-container text-app-primary">内容</div>
```

### 从颜色对象迁移到 Tailwind 类名
```typescript
// 旧方式
const style = {
  backgroundColor: colors.background.container,
  color: colors.text.primary,
  borderColor: colors.border.light,
};

// 新方式 - 直接使用 Tailwind 类名
<div className="bg-app-container text-app-primary border border-app-light">
```

现在颜色对象与 Tailwind CSS 配置完全一致，你可以根据需要选择使用 TypeScript 颜色对象或 Tailwind 类名！
