import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
// 从本地导入，但使用动态导入优化
import SyntaxHighlighter from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

interface MarkdownReaderProps {
  content: string;
}

export default function MarkdownReader({ content }: MarkdownReaderProps) {
  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw]}
      remarkPlugins={[remarkGfm]}
      components={{
        h1: ({ ...props }) => (
          <h1
            style={{
              fontSize: "2rem",
              fontWeight: "bold",
              margin: "1.5rem 0",
            }}
            {...props}
          />
        ),
        h2: ({ ...props }) => (
          <h2
            style={{
              fontSize: "1.5rem",
              fontWeight: "bold",
              margin: "1rem 0",
            }}
            {...props}
          />
        ),
        h3: ({ ...props }) => (
          <h3
            style={{
              fontSize: "1.25rem",
              fontWeight: "bold",
              margin: "1rem 0",
            }}
            {...props}
          />
        ),
        h4: ({ ...props }) => (
          <h4
            style={{
              fontSize: "1.1rem",
              fontWeight: "bold",
              margin: "0.8rem 0",
            }}
            {...props}
          />
        ),
        code({
          inline,
          className,
          children,
          ...props
        }: {
          inline?: boolean;
          className?: string;
          children?: React.ReactNode;
        }) {
          const match = /language-(\w+)/.exec(className || "");
          const codeContent = String(children).replace(/\n$/, "");

          if (!inline) {
            const cleanCode = codeContent.replace(/^```[\w-]*\n|```$/g, "");
            return (
              <SyntaxHighlighter
                style={vscDarkPlus}
                language={match ? match[1] : "text"}
                PreTag="div"
                {...props}
              >
                {cleanCode}
              </SyntaxHighlighter>
            );
          }

          return (
            <code className={className} {...props}>
              {codeContent}
            </code>
          );
        },
        li(props: React.LiHTMLAttributes<HTMLLIElement> & { ordered?: boolean }) {
          const { ordered, ...rest } = props;
          return (
            <li
              {...rest}
              className={`ml-4 ${
                ordered ? "list-decimal marker:font-bold" : "list-disc"
              }`}
            />
          );
        },
        ol({ ...props }) {
          return <ol {...props} className="ml-4 list-disc" />;
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
}
