import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Form, Input, Button } from 'antd';
import { X, Smartphone, Lock, Loader2, ExternalLink } from 'lucide-react';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoginSuccess: () => void;
  onSwitchToRegister: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({
  isOpen,
  onClose,
  onLoginSuccess,
  onSwitchToRegister
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    setIsLoading(true);

    // 模拟登录请求
    setTimeout(() => {
      setIsLoading(false);
      onLoginSuccess();
      onClose();
      form.resetFields();
    }, 2000);
  };

  const handleOALogin = () => {
    // OA登录链接处理
    window.location.href = import.meta.env.VITE_LOGIN_BASE_URL 
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ duration: 0.3 }}
          onClick={(e) => e.stopPropagation()}
          className="bg-gray-900/95 backdrop-blur-xl rounded-2xl border border-gray-700/50 p-8 w-full max-w-md shadow-2xl shadow-blue-500/10"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold text-white mb-1">手机号登录</h2>
              <p className="text-gray-400 text-sm">登录多麦AI，开启智能广告策略之旅</p>
            </div>
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={onClose}
              className="p-2 hover:bg-gray-800/50 rounded-xl transition-colors border border-gray-700/50 hover:border-gray-600/50"
            >
              <X className="w-5 h-5 text-gray-400" />
            </motion.button>
          </div>

          {/* OA登录链接 */}
          <div className="mb-6">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleOALogin}
              className="w-full flex items-center justify-center space-x-2 p-3 bg-gray-800/30 hover:bg-gray-700/30 rounded-xl transition-all duration-300 border border-gray-700/50 hover:border-gray-600/50 text-gray-300 hover:text-white"
            >
              <ExternalLink className="w-4 h-4" />
              <span className="text-sm font-medium">OA登录</span>
            </motion.button>
          </div>

          {/* 分割线 */}
          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700/50"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-900/95 text-gray-400">或使用手机号登录</span>
            </div>
          </div>

          {/* 登录表单 */}
          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            className="space-y-6"
          >
            {/* 手机号输入 */}
            <Form.Item
              name="phone"
              label={<span className="text-gray-300 text-sm font-medium">手机号</span>}
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]}
              className="mb-6"
            >
              <Input
                prefix={<Smartphone className="h-5 w-5 text-gray-500" />}
                placeholder="请输入手机号码"
                className="h-12 backdrop-blur-sm"
              />
            </Form.Item>

            {/* 密码输入 */}
            <Form.Item
              name="password"
              label={
                <div className="flex items-center justify-between w-full">
                  <span className="text-gray-300 text-sm font-medium">密码</span>
                  <motion.button
                    type="button"
                    whileHover={{ scale: 1.05 }}
                    className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    获取密码
                  </motion.button>
                </div>
              }
              rules={[
                { required: true, message: '请输入登录密码' },
                { min: 6, message: '密码至少6位' }
              ]}
              className="mb-6"
            >
              <Input.Password
                prefix={<Lock className="h-5 w-5 text-gray-500" />}
                placeholder="请输入登录密码"
                className="h-12 backdrop-blur-sm"
              />
            </Form.Item>

            {/* 登录按钮 */}
            <Form.Item className="mb-0">
              <motion.div
                whileHover={{
                  scale: 1.02,
                  boxShadow: "0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)"
                }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={isLoading}
                  className="w-full bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 hover:from-cyan-400 hover:via-blue-400 hover:to-purple-500 text-white font-semibold py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-blue-500/25 relative overflow-hidden group border-none h-12"
                  icon={isLoading ? <Loader2 className="w-5 h-5 animate-spin" /> : null}
                >
                  {isLoading ? '登录中...' : '登录'}
                </Button>
              </motion.div>
            </Form.Item>
          </Form>

          {/* 注册链接 */}
          <div className="mt-6 text-center">
            <p className="text-gray-400 text-sm">
              尚未通过手机注册？{' '}
              <motion.button
                whileHover={{ scale: 1.05 }}
                onClick={onSwitchToRegister}
                className="text-blue-400 hover:text-blue-300 transition-colors font-medium"
              >
                注册
              </motion.button>
            </p>
          </div>

          {/* 底部装饰 */}
          <div className="mt-8 pt-6 border-t border-gray-700/50">
            <div className="flex items-center justify-center space-x-2 text-gray-500">
              <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full"></div>
              <span className="text-xs">安全登录 · 数据加密</span>
              <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default LoginModal;