import React, { useEffect, useState, useCallback } from "react";
import { motion } from "framer-motion";
import { Form, Input, Button, Cascader, GetProps } from "antd";
import { <PERSON>rk<PERSON>, Loader } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { useFormData, useProgressStore } from "@/contexts/ProgressContext";
import { strategyService } from "@/service";

// 定义级联选项接口
interface CascaderOption {
  value: string;
  label: string;
  id?: number;
  parentId?: number;
  level?: number;
  status?: boolean;
  children?: CascaderOption[];
}

type Field = {
  key: string;
  label: string;
  render: React.ReactNode;
  formProps?: GetProps<typeof Form.Item>;
};

// 行业级联数据
const StrategyGenerator: React.FC = () => {
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const formData = useFormData();
  const [form] = Form.useForm();
  const [industryCategoriesOptions, setIndustryCategoriesOptions] = useState<
    CascaderOption[]
  >([]);
  const { startProgress, showResults: generated } = useProgressStore();

  const handleSubmit = async (values: Record<string, unknown>) => {
    const value = await form.validateFields(values);
    setIsLoading(true);

    const {
      d: { taskId },
    } = await strategyService.generateStrategy({
      ...value,
      categoryId: value.categoryId?.[value.categoryId.length - 1],
    });
    startProgress(value, taskId);
    setIsLoading(false);
  };

  const inputFields: Field[] = [
    {
      key: "categoryId",
      label: t("form.industry"),
      render: (
        <Cascader
          options={industryCategoriesOptions}
          placeholder="请选择行业分类"
          showSearch
          showCheckedStrategy="SHOW_CHILD"
          className="backdrop-blur-sm"
          expandTrigger="hover"
          displayRender={(labels: string[]) => labels.join(" > ")}
          styles={{
            root: {
              height: 47,
            },
          }}
          classNames={{
            root: "backdrop-blur-sm [&_.ant-select-selector]:!px-4",
          }}
        />
      ),
    },
    {
      key: "brandName",
      label: t("form.brand"),
      render: (
        <Input
          placeholder="请输入品牌名称,例如：Apple, 小米, 华为..."
          className="backdrop-blur-sm"
        />
      ),
    },
    {
      key: "brandDescription",
      label: t("form.brandDescription"),
      render: (
        <Input.TextArea
          placeholder="例如：创新科技, 高端品质, 用户至上..."
          rows={3}
          className="backdrop-blur-sm"
        />
      ),
    },
    {
      key: "productName",
      label: t("form.productName"),
      render: (
        <Input
          placeholder="请输入产品名称,例如：iPhone 15, 小米平板..."
          className="backdrop-blur-sm"
        />
      ),
    },
    // {
    //   key: "audience",
    //   label: t("form.audience"),
    //   type: "input",
    //   props: {
    //     placeholder: "例如：18-35岁都市白领, 学生群体...",
    //     className: "backdrop-blur-sm",
    //   },
    // },
    // {
    //   key: "competitors",
    //   label: t("form.competitors"),
    //   type: "input",
    //   props: {
    //     placeholder: "例如：竞品A, 竞品B...",
    //     className: "backdrop-blur-sm",
    //   },
    // },
    // {
    //   key: "budget",
    //   label: t("form.budget"),
    //   type: "input",
    //   props: {
    //     placeholder: "例如：10万-50万元",
    //     className: "backdrop-blur-sm",
    //   },
    // },
    {
      key: "productDescription",
      label: t("form.features"),
      render: (
        <Input.TextArea
          placeholder="例如：高性价比, 创新设计, 优质服务..."
          rows={3}
          className="backdrop-blur-sm"
        />
      ),
    },
  ];

  // 定义一个函数用于获取行业分类数据，并使用useCallback缓存函数引用
  const fetchIndustryCategories = useCallback(async () => {
    try {
      const { d } = await strategyService.getIndustryCategories();
      if (d) {
        // 1. 创建一个map来存储所有项目
        const itemMap = new Map();

        // 2. 首先将所有项添加到map
        d.forEach((item) => {
          itemMap.set(item.id, {
            value: item.id,
            label: item.name,
            id: item.id,
            parentId: item.parentId,
            level: item.level,
            status: item.status,
            children: [],
          });
        });

        // 3. 构建层级结构
        const result: CascaderOption[] = [];

        // 4. 遍历所有项目
        itemMap.forEach((item) => {
          if (item.parentId === 0) {
            // 这是顶级项目
            result.push(item);
          } else {
            // 这是子项目，将其添加到父项目的children中
            const parentItem = itemMap.get(item.parentId);
            if (parentItem) {
              parentItem.children.push(item);
            }
          }
        });

        // 5. 移除空的children数组
        const cleanEmptyChildren = (items: CascaderOption[]) => {
          items.forEach((item) => {
            if (item.children && item.children.length === 0) {
              delete item.children;
            } else if (item.children && item.children.length > 0) {
              cleanEmptyChildren(item.children);
            }
          });
        };

        cleanEmptyChildren(result);

        setIndustryCategoriesOptions(result);
      }
    } catch (error) {
      console.error("获取行业分类失败:", error);
    }
  }, []);

  useEffect(() => {
    fetchIndustryCategories();
  }, [fetchIndustryCategories]);

  return (
    <div className="h-full flex flex-col">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex-1"
      >
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-white mb-2">
            AI广告投放策略生成器
          </h2>
          <p className="text-app-secondary text-xs">
            输入您的品牌信息，让AI为您生成专业的社交媒体广告策略
          </p>
        </div>

        <Form
          form={form}
          onFinish={handleSubmit}
          layout="vertical"
          className="space-y-4 flex-1"
          requiredMark={false}
          initialValues={{
            ...formData,
          }}
        >
          {inputFields.map((field, index) => (
            <motion.div
              key={field.key}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Form.Item
                name={field.key}
                label={field.label}
                className="mb-6"
                required
                rules={[
                  {
                    required: true,
                    message: `请输入${field.label}`,
                  },
                ]}
              >
                {field.render}
              </Form.Item>
            </motion.div>
          ))}

          <Form.Item className="mb-0 mt-6">
            <motion.div
              whileHover={{
                scale: 1.02,
                boxShadow:
                  "0 0 25px rgba(59, 130, 246, 0.6), 0 0 50px rgba(147, 51, 234, 0.4)",
              }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                className="w-full bg-gradient-button text-white font-semibold py-3 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed shadow-app-menu relative overflow-hidden group border-none h-12"
                icon={
                  isLoading ? (
                    <Loader className="w-4 h-4 animate-spin" />
                  ) : (
                    <Sparkles className="w-4 h-4" />
                  )
                }
              >
                {isLoading
                  ? "AI正在生成策略..."
                  : generated
                  ? t("form.generated")
                  : t("form.generate")}
              </Button>
            </motion.div>
          </Form.Item>
        </Form>
      </motion.div>
    </div>
  );
};

export default StrategyGenerator;
