import { defineConfig, loadEnv, splitVendorChunkPlugin } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载 .env, .env.local, .env.[mode], .env.[mode].local
  const env = loadEnv(mode, process.cwd());

  console.log(
    `Building for ${mode} environment: ${env.VITE_ENV || "development"}`
  );
  return {
    server: {
      port: 5173,
      proxy: {
        "/api": {
          target: "https://hotmai.sk8s.cn",
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    plugins: [
      react(),
      splitVendorChunkPlugin(), // 拆分第三方库到单独的chunk
      visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        filename: "dist/stats.html", // 分析报告的文件名
      }),
    ],
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
      },
    },
    optimizeDeps: {
      exclude: ["lucide-react"],
    },
    define: {
      // 确保环境变量能被替换
      __APP_ENV__: JSON.stringify(env.VITE_ENV || "development"),
    },
    build: {
      // 输出目录
      outDir: "dist",
      // 启用 sourcemap
      sourcemap: mode !== "production",
      // 设置 chunk 大小警告的限制
      chunkSizeWarningLimit: 1000,
      // 清空输出目录
      emptyOutDir: true,
      // 自定义构建配置
      rollupOptions: {
        output: {
          // 手动指定各个 chunk
          manualChunks: (id: string) => {
            // React 相关库
            if (
              id.includes("node_modules/react/") ||
              id.includes("node_modules/react-dom/") ||
              id.includes("node_modules/react-router/") ||
              id.includes("node_modules/react-router-dom/")
            ) {
              return "react-vendor";
            }

            // UI 组件库
            if (
              id.includes("node_modules/antd/") ||
              id.includes("node_modules/@ant-design/") ||
              id.includes("node_modules/framer-motion/")
            ) {
              return "ui-vendor";
            }

            // 工具库
            if (id.includes("node_modules/axios/")) {
              return "utils-vendor";
            }

            // Markdown 相关
            if (id.includes("node_modules/react-markdown/")) {
              return "react-markdown";
            }

            // Markdown 相关插件
            if (
              id.includes("node_modules/rehype-raw/") ||
              id.includes("node_modules/remark-gfm/") ||
              id.includes("node_modules/highlight.js/") ||
              id.includes("node_modules/react-syntax-highlighter/")
            ) {
              return "markdown-vendor";
            }

            // 其他第三方库 - 所有其他 node_modules 中的模块都归到这里
            if (id.includes("node_modules/")) {
              return "other-vendor";
            }
            // 非第三方库代码（应用自身的代码）保持默认分包
            return undefined;
          },
          // 自定义 chunk 文件名格式
          chunkFileNames: "assets/js/[name]-[hash].js",
          // 自定义入口文件名格式
          entryFileNames: "assets/js/[name]-[hash].js", // 自定义静态资源文件名格式
          assetFileNames: (assetInfo: { name?: string }) => {
            if (!assetInfo.name) return "assets/other/[name]-[hash][extname]";

            const info = assetInfo.name.split(".");
            let extType = info[info.length - 1];

            if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(assetInfo.name)) {
              extType = "images";
            } else if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
              extType = "fonts";
            } else if (
              /\.(mp4|webm|ogg|mp3|wav|flac|aac)$/.test(assetInfo.name)
            ) {
              extType = "media";
            } else if (/\.css$/.test(assetInfo.name)) {
              extType = "css";
            }

            return `assets/${extType}/[name]-[hash][extname]`;
          },
        },
      },
      // 配置 CSS 相关
      cssCodeSplit: true,
      // 压缩配置
      minify: "terser",
      terserOptions: {
        compress: {
          // 生产环境下移除 console
          drop_console: mode === "production",
          drop_debugger: mode === "production",
        },
      },
    },
  };
});
