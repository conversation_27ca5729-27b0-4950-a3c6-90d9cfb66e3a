import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
import { message } from "antd";
import { getDynamicCorsConfig, corsRequestInterceptor } from "./cors";
import useLoginStore from "@/contexts/LoginContext";

// 定义响应数据的通用接口
export interface ApiResponse<T = unknown> {
  d: T;
  m: string;
  f?: number; // 可选的成功标识字段
  e: any;
}

// 定义请求配置接口
export interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean;
  showError?: boolean;
  showSuccess?: boolean;
}

// 创建axios实例
const createAxiosInstance = (): AxiosInstance => {
  // 获取动态跨域配置
  const corsConfig = getDynamicCorsConfig();

  const instance = axios.create({
    ...corsConfig,
    timeout: 10000,
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      config = corsRequestInterceptor(config);
      // 添加请求时间戳，防止缓存
      if (config.method === "get") {
        config.params = {
          ...config.params,
          _t: Date.now(),
        };
      }
      return config;
    },
    (error: AxiosError) => {
      console.error("请求配置错误:", error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const customConfig = response.config as RequestConfig;

      const { data } = response;
      // 处理业务逻辑错误
      if (data.f !== 1) {
        const errorMessage = data.m || "请求失败";

        // 显示错误提示
        if (customConfig.showError !== false) {
          message.error(errorMessage);
        }

        return Promise.reject(new Error(errorMessage));
      }

      // 显示成功提示
      if (customConfig.showSuccess && data.m) {
        message.success(data.m);
      }

      // 返回完整的响应对象以符合TypeScript类型要求
      return response;
    },
    (error: AxiosError<ApiResponse>) => {
      const customConfig = error.config as RequestConfig;

      let errorMessage = "网络错误，请稍后重试";

      if (error.response) {
        // 服务器响应了错误状态码
        const { status, data } = error.response;
        const { toLogin } = useLoginStore();
        switch (status) {
          case 400:
            errorMessage = data?.m || "请求参数错误";
            break;
          case 401:
            errorMessage = "登录已过期，请重新登录";
            // 清除cookie
            document.cookie =
              "OA_ACCESS=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";

            // 这里可以触发登录弹窗或跳转
            toLogin(true);
            break;
          case 403:
            errorMessage = "没有权限访问该资源";
            break;
          case 404:
            errorMessage = "请求的资源不存在";
            break;
          case 500:
            errorMessage = "服务器内部错误";
            break;
          case 502:
            errorMessage = "网关错误";
            break;
          case 503:
            errorMessage = "服务暂时不可用";
            break;
          default:
            errorMessage = data?.m || `请求失败 (${status})`;
        }
      } else if (error.request) {
        // 请求已发出但没有收到响应 - 可能是跨域问题
        errorMessage = "网络连接超时，请检查网络或跨域配置";
      } else {
        // 其他错误
        errorMessage = error.message || "请求配置错误";
      }

      // 显示错误提示
      if (customConfig?.showError !== false) {
        message.error(errorMessage);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// 创建请求实例
const request = createAxiosInstance();

// 封装常用的请求方法
export const api = {
  // GET请求
  get: <T = unknown>(
    url: string,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    return request.get(url, config).then((response) => response.data);
  },

  // POST请求
  post: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    return request.post(url, data, config).then((response) => response.data);
  },

  // PUT请求
  put: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    return request.put(url, data, config).then((response) => response.data);
  },

  // DELETE请求
  delete: <T = unknown>(
    url: string,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    return request.delete(url, config).then((response) => response.data);
  },

  // PATCH请求
  patch: <T = unknown>(
    url: string,
    data?: unknown,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    return request.patch(url, data, config).then((response) => response.data);
  },

  // 文件上传
  upload: <T = unknown>(
    url: string,
    file: File,
    config?: RequestConfig
  ): Promise<ApiResponse<T>> => {
    const formData = new FormData();
    formData.append("file", file);

    return request.post(url, formData, {
      ...config,
      headers: {
        "Content-Type": "multipart/form-data",
        ...config?.headers,
      },
    });
  },

  // 下载文件
  download: (
    url: string,
    filename?: string,
    config?: RequestConfig
  ): Promise<void> => {
    return request
      .get(url, {
        ...config,
        responseType: "blob",
      })
      .then((response: AxiosResponse) => {
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename || "download";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      });
  },
};

// 导出默认实例
export default request;
