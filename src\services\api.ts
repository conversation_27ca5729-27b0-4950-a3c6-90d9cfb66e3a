import { api } from '../utils/request';

// 定义API接口的数据类型
export interface LoginParams {
  username: string;
  password: string;
}

export interface RegisterParams {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: string;
}

export interface StrategyParams {
  brand: string;
  audience: string;
  competitors: string;
  industry: string;
  budget: string;
  features: string;
}

export interface StrategyResult {
  id: string;
  coreStrategy: string;
  contentPlan: string;
  influencerStrategy: string;
  createdAt: string;
}

export interface SentimentAnalysisParams {
  content: string;
  platform?: string;
}

export interface SentimentResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  score: number;
  keywords: string[];
  suggestions: string[];
}

export interface ContentGenerationParams {
  topic: string;
  platform: string;
  style: string;
  length: number;
}

export interface ContentResult {
  title: string;
  content: string;
  hashtags: string[];
  suggestions: string[];
}

export interface InfluencerParams {
  industry: string;
  budget: string;
  audience: string;
  platform: string;
}

export interface InfluencerResult {
  id: string;
  name: string;
  platform: string;
  followers: number;
  engagement: number;
  price: number;
  tags: string[];
}

// 用户认证相关API
export const authAPI = {
  // 用户登录
  login: (params: LoginParams) => {
    return api.post<{ token: string; user: UserInfo }>('/auth/login', params, {
      showSuccess: true,
      showError: true,
    });
  },

  // 用户注册
  register: (params: RegisterParams) => {
    return api.post<{ token: string; user: UserInfo }>('/auth/register', params, {
      showSuccess: true,
      showError: true,
    });
  },

  // 获取用户信息
  getUserInfo: () => {
    return api.get<UserInfo>('/auth/user', {
      showError: true,
    });
  },

  // 用户登出
  logout: () => {
    return api.post('/auth/logout', {}, {
      showSuccess: true,
    });
  },

  // 刷新token
  refreshToken: () => {
    return api.post<{ token: string }>('/auth/refresh', {}, {
      showError: false,
    });
  },
};

// 广告策略相关API
export const strategyAPI = {
  // 生成广告策略
  generateStrategy: (params: StrategyParams) => {
    return api.post<StrategyResult>('/strategy/generate', params, {
      showLoading: true,
      showError: true,
    });
  },

  // 获取策略历史
  getStrategyHistory: (page = 1, pageSize = 10) => {
    return api.get<{ list: StrategyResult[]; total: number }>('/strategy/history', {
      params: { page, pageSize },
      showError: true,
    });
  },

  // 获取策略详情
  getStrategyDetail: (id: string) => {
    return api.get<StrategyResult>(`/strategy/${id}`, {
      showError: true,
    });
  },

  // 删除策略
  deleteStrategy: (id: string) => {
    return api.delete(`/strategy/${id}`, {
      showSuccess: true,
      showError: true,
    });
  },
};

// 情感分析相关API
export const sentimentAPI = {
  // 分析评论情感
  analyzeSentiment: (params: SentimentAnalysisParams) => {
    return api.post<SentimentResult>('/sentiment/analyze', params, {
      showLoading: true,
      showError: true,
    });
  },

  // 批量分析
  batchAnalyze: (contents: string[]) => {
    return api.post<SentimentResult[]>('/sentiment/batch', { contents }, {
      showLoading: true,
      showError: true,
    });
  },
};

// 内容生成相关API
export const contentAPI = {
  // 生成爆文内容
  generateContent: (params: ContentGenerationParams) => {
    return api.post<ContentResult>('/content/generate', params, {
      showLoading: true,
      showError: true,
    });
  },

  // 获取内容模板
  getTemplates: (platform?: string) => {
    return api.get<{ id: string; name: string; template: string }[]>('/content/templates', {
      params: { platform },
      showError: true,
    });
  },

  // 保存生成的内容
  saveContent: (content: ContentResult) => {
    return api.post('/content/save', content, {
      showSuccess: true,
      showError: true,
    });
  },
};

// 达人推荐相关API
export const influencerAPI = {
  // 获取达人推荐
  getRecommendations: (params: InfluencerParams) => {
    return api.post<InfluencerResult[]>('/influencer/recommend', params, {
      showLoading: true,
      showError: true,
    });
  },

  // 获取达人详情
  getInfluencerDetail: (id: string) => {
    return api.get<InfluencerResult & { bio: string; recentPosts: any[] }>(`/influencer/${id}`, {
      showError: true,
    });
  },

  // 收藏达人
  favoriteInfluencer: (id: string) => {
    return api.post(`/influencer/${id}/favorite`, {}, {
      showSuccess: true,
      showError: true,
    });
  },

  // 获取收藏的达人
  getFavorites: () => {
    return api.get<InfluencerResult[]>('/influencer/favorites', {
      showError: true,
    });
  },
};

// 通用API
export const commonAPI = {
  // 获取行业列表
  getIndustries: () => {
    return api.get<{ value: string; label: string; children?: any[] }[]>('/common/industries', {
      showError: true,
    });
  },

  // 文件上传
  uploadFile: (file: File) => {
    return api.upload<{ url: string; filename: string }>('/common/upload', file, {
      showLoading: true,
      showError: true,
    });
  },

  // 获取系统配置
  getConfig: () => {
    return api.get<{ [key: string]: any }>('/common/config', {
      showError: false,
    });
  },
};

// 导出所有API
export default {
  auth: authAPI,
  strategy: strategyAPI,
  sentiment: sentimentAPI,
  content: contentAPI,
  influencer: influencerAPI,
  common: commonAPI,
};
