import { api } from '../utils/request';

// 用户登录参数
export interface LoginParams {
  username: string;
  password: string;
}

// 用户注册参数
export interface RegisterParams {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 用户信息
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  createdAt: string;
}

// 用户API服务
export const userService = {
  // 用户登录
  login: (params: LoginParams) => {
    return api.post<{ token: string; user: UserInfo }>('/auth/login', params, {
      showSuccess: true,
      showError: true,
    });
  },

  // 用户注册
  register: (params: RegisterParams) => {
    return api.post<{ token: string; user: UserInfo }>('/auth/register', params, {
      showSuccess: true,
      showError: true,
    });
  },

  // 获取用户信息
  getUserInfo: () => {
    return api.get<UserInfo>('/user/info');
  },

  // 更新用户信息
  updateUserInfo: (data: Partial<UserInfo>) => {
    return api.put<UserInfo>('/user/info', data, {
      showSuccess: true,
    });
  },

  // 修改密码
  changePassword: (data: { oldPassword: string; newPassword: string }) => {
    return api.put('/user/password', data, {
      showSuccess: true,
    });
  },

  // 退出登录
  logout: () => {
    return api.post('/auth/logout', null, {
      showSuccess: true,
    });
  }
};
