# 🚀 多麦AI广告策略平台

> 基于 React + TypeScript + AI 的智能社交媒体广告策略生成平台

[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.2-646CFF.svg)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC.svg)](https://tailwindcss.com/)
[![Zustand](https://img.shields.io/badge/Zustand-5.0.5-orange.svg)](https://github.com/pmndrs/zustand)

## 📋 项目概述

多麦AI广告策略平台是一个现代化的 Web 应用，专为品牌方和营销人员提供智能化的社交媒体广告策略生成服务。平台集成了AI技术，能够根据品牌信息、目标受众、行业特点等因素，自动生成专业的广告投放策略、内容规划和达人推荐方案。

### 🎯 核心功能

- **🧠 AI广告策略生成** - 基于品牌信息智能生成投放策略
- **📊 评论情感分析** - 分析用户评论的情感倾向和关键词
- **✍️ 爆文内容生成** - AI驱动的社交媒体内容创作
- **👥 达人推荐系统** - 智能匹配适合的KOL/KOC资源
- **🌐 多语言支持** - 支持中英文双语切换
- **📱 响应式设计** - 完美适配移动端和桌面端

## 🏗️ 技术架构

### 核心技术栈

```
前端框架: React 18.3.1 + TypeScript 5.5.3
构建工具: Vite 5.4.2
样式方案: Tailwind CSS 3.4.1 + PostCSS
动画库: Framer Motion 10.16.16
状态管理: Zustand 5.0.5
路由管理: React Router v7.6.2
UI组件: Ant Design 5.26.1
图标库: Lucide React 0.344.0
HTTP客户端: Axios 1.10.0
代码规范: ESLint + TypeScript ESLint
```

### 项目结构

```
src/
├── components/          # 组件目录
│   ├── auth/           # 认证相关组件
│   ├── common/         # 通用组件
│   └── ...
├── contexts/           # React Context
│   ├── LanguageContext.tsx    # 多语言上下文
│   ├── LoginContext.tsx       # 登录状态管理
│   └── ProgressContext.tsx    # 进度状态管理
├── pages/              # 页面组件
│   ├── StrategyPage/   # 策略生成页面
│   ├── SentimentPage/  # 情感分析页面
│   ├── ContentPage/    # 内容生成页面
│   └── InfluencerPage/ # 达人推荐页面
├── router/             # 路由配置
├── services/           # API服务层
├── utils/              # 工具函数
└── styles/             # 样式文件
```

## 🎨 代码亮点

### 1. 先进的状态管理架构

使用 **Zustand** 实现轻量级、类型安全的状态管理：

```typescript
// 支持状态持久化和 DevTools
export const useProgressStore = create<ProgressState>()(
  devtools(
    persist(
      (set) => ({
        // 状态定义
        showProgress: false,
        showResults: false,
        strategyData: null,
        
        // 方法定义
        startProgress: (formData, taskId) => set({
          formData, showProgress: true, taskId
        }),
        
        completeProgress: (strategyData) => set({
          strategyData, showProgress: false, showResults: true
        })
      }),
      {
        name: "progress-storage",
        storage: createJSONStorage(() => sessionStorage)
      }
    )
  )
);
```

### 2. 优雅的用户体验设计

**打字机效果**：策略生成过程中的实时文本动画
```typescript
const typeWriter = useCallback((text: string, callback?: () => void) => {
  setIsTyping(true);
  let index = 0;
  
  const type = () => {
    if (index < text.length) {
      setGeneratedContent(text.slice(0, index + 1));
      index++;
      timeoutIdRef.current = window.setTimeout(type, 30);
    } else {
      setIsTyping(false);
      callback?.();
    }
  };
  
  type();
}, []);
```

**页面可见性检测**：用户离开页面时智能计算进度
```typescript
useEffect(() => {
  const handleVisibilityChange = () => {
    if (document.hidden) {
      pageHiddenTimeRef.current = Date.now();
    } else if (pageHiddenTimeRef.current && isTyping) {
      const awayTime = Date.now() - pageHiddenTimeRef.current;
      const { targetStep, targetCharIndex } = calculateProgressByTime(awayTime);
      // 智能更新进度...
    }
  };
  
  document.addEventListener('visibilitychange', handleVisibilityChange);
  return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
}, [isTyping]);
```

### 3. 强大的 API 基础设施

**统一错误处理**和**请求拦截器**：
```typescript
// 响应拦截器 - 统一处理业务逻辑错误
instance.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response;
    if (data.f !== 1) {
      const errorMessage = data.m || "请求失败";
      if (customConfig.showError !== false) {
        message.error(errorMessage);
      }
      return Promise.reject(new Error(errorMessage));
    }
    return response;
  },
  (error: AxiosError) => {
    // 详细的错误处理逻辑...
  }
);
```

### 4. 模块化的组件设计

**懒加载路由**提升性能：
```typescript
// 懒加载各个页面组件
export const StrategyPage = lazy(() => import('../pages/StrategyPage/index'));
export const SentimentPage = lazy(() => import('../pages/SentimentPage/index'));
export const ContentPage = lazy(() => import('../pages/ContentPage/index'));
```

**完整的多语言支持**：
```typescript
const translations = {
  zh: {
    'nav.strategy': 'AI广告投放策略',
    'nav.sentiment': '评论情感分析',
    'nav.content': '爆文生成',
    'nav.influencer': '达人推荐'
  },
  en: {
    'nav.strategy': 'AI Ad Strategy',
    'nav.sentiment': 'Sentiment Analysis',
    'nav.content': 'Content Generator',
    'nav.influencer': 'Influencer Recommendation'
  }
};
```

## 🔄 核心流程

### 策略生成流程

1. **用户输入** - 填写品牌信息（品牌名称、目标受众、行业、预算等）
2. **表单验证** - 前端验证必填字段和数据格式
3. **提交请求** - 调用策略生成API，获取任务ID
4. **进度展示** - 6步骤进度动画，包含打字机效果
5. **结果展示** - 分步骤展示核心策略、内容方案、达人策略
6. **结果操作** - 支持复制、分享、重新生成

### 6步骤进度展示

```
步骤1: 正在解读营销需求 (2秒)
步骤2: 正在查阅品牌行业资料 (2.5秒)
步骤3: 正在生成整体性创意 (3秒)
步骤4: 正在制定营销策略方案 (2.8秒)
步骤5: 正在制定达人策略 (2.2秒)
步骤6: 正在推荐候选达人 (2秒)
```

## 📊 系统架构图

### 整体架构
```mermaid
graph TB
    subgraph "前端应用层"
        A[React 18 + TypeScript]
        B[Vite 构建工具]
        C[Tailwind CSS]
        D[Framer Motion]
    end

    subgraph "路由管理"
        E[React Router v7]
        F[懒加载路由]
        G[路由守卫]
    end

    subgraph "状态管理"
        H[Zustand Store]
        I[ProgressContext]
        J[LoginContext]
        K[LanguageContext]
    end

    subgraph "组件架构"
        L[Layout 布局]
        M[Header 头部]
        N[Sidebar 侧边栏]
        O[页面组件]
        P[通用组件]
    end

    subgraph "服务层"
        Q[API 服务]
        R[请求拦截器]
        S[响应拦截器]
        T[错误处理]
    end

    subgraph "后端 API"
        U[策略生成 API]
        V[情感分析 API]
        W[内容生成 API]
        X[达人推荐 API]
        Y[用户认证 API]
    end

    A --> E
    A --> H
    A --> L
    E --> O
    H --> I
    H --> J
    H --> K
    L --> M
    L --> N
    L --> O
    O --> P
    O --> Q
    Q --> R
    Q --> S
    Q --> T
    Q --> U
    Q --> V
    Q --> W
    Q --> X
    Q --> Y
```

### 策略生成时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 表单组件
    participant S as Zustand Store
    participant P as 进度组件
    participant A as API 服务
    participant B as 后端服务

    U->>F: 填写品牌信息
    F->>F: 表单验证
    U->>F: 点击生成策略
    F->>A: 提交策略生成请求
    A->>B: POST /strategy/generate
    B-->>A: 返回 taskId
    A-->>F: 返回任务ID
    F->>S: startProgress(formData, taskId)
    S->>P: 显示进度界面

    loop 进度展示
        P->>P: 步骤1: 解读营销需求
        P->>P: 打字机效果显示内容
        P->>P: 步骤2: 查阅行业资料
        P->>P: 打字机效果显示内容
        P->>P: 步骤3: 生成整体创意
        P->>P: 打字机效果显示内容
        P->>P: 步骤4: 制定营销策略
        P->>P: 打字机效果显示内容
        P->>P: 步骤5: 制定达人策略
        P->>P: 打字机效果显示内容
        P->>P: 步骤6: 推荐候选达人
        P->>P: 打字机效果显示内容
    end

    P->>S: completeProgress(strategyData)
    S->>U: 显示策略结果
```

### 用户交互流程
```mermaid
flowchart TD
    A[用户访问应用] --> B{是否已登录?}
    B -->|否| C[显示登录模态框]
    B -->|是| D[进入主界面]
    C --> E[用户登录/注册]
    E --> D

    D --> F[选择功能模块]
    F --> G[AI广告策略]
    F --> H[评论情感分析]
    F --> I[爆文生成]
    F --> J[达人推荐]

    G --> K{选择输入方式}
    K -->|手动填写| L[填写表单]
    K -->|演示案例| M[选择演示卡片]

    L --> N[提交生成请求]
    M --> N
    N --> O[显示进度动画]
    O --> P[6步骤进度展示]
    P --> Q[显示策略结果]

    Q --> R[查看核心策略]
    Q --> S[查看内容方案]
    Q --> T[查看达人策略]

    R --> U[复制/分享结果]
    S --> U
    T --> U
    U --> V[生成新策略]
    V --> K

    H --> W[开发中页面]
    I --> X[开发中页面]
    J --> Y[开发中页面]
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 指定环境启动
npm run dev:test    # 测试环境
npm run dev:prod    # 生产环境
```

### 构建部署

```bash
# 构建生产版本
npm run build:prod

# 构建并分析包大小
npm run build:analyze

# 预览构建结果
npm run preview:prod
```

### 代码规范

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix

# 依赖分析
npm run analyze:deps
```

## 📊 API 接口

### 策略生成相关

```typescript
// 生成广告策略
POST /strategy/generate
{
  brand: string;        // 品牌名称
  audience: string;     // 目标受众
  competitors: string;  // 竞品分析
  industry: string;     // 所属行业
  budget: string;       // 投放预算
  features: string;     // 产品卖点
}

// 获取策略历史
GET /strategy/history?page=1&pageSize=10

// 获取策略详情
GET /strategy/:id
```

### 其他功能模块

```typescript
// 情感分析
POST /sentiment/analyze
{
  content: string;      // 分析内容
  platform?: string;   // 平台类型
}

// 内容生成
POST /content/generate
{
  topic: string;        // 内容主题
  platform: string;    // 发布平台
  style: string;        // 内容风格
  length: number;       // 内容长度
}

// 达人推荐
POST /influencer/recommend
{
  industry: string;     // 行业类型
  budget: string;       // 预算范围
  audience: string;     // 目标受众
  platform: string;    // 平台类型
}
```

## 🎯 开发计划

### 已完成功能 ✅

- [x] 基础项目架构搭建
- [x] AI广告策略生成核心功能
- [x] 响应式UI设计和动画效果
- [x] 多语言支持框架
- [x] 状态管理和数据持久化
- [x] 路由系统和懒加载
- [x] API服务层和错误处理

### 开发中功能 🚧

- [ ] 评论情感分析功能
- [ ] 爆文内容生成功能  
- [ ] 达人推荐系统
- [ ] 用户认证和权限管理
- [ ] 数据可视化图表
- [ ] 策略分享和导出功能

### 计划功能 📋

- [ ] 实时协作编辑
- [ ] 策略模板库
- [ ] 效果数据追踪
- [ ] 移动端原生应用
- [ ] 第三方平台集成

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 团队

- **项目负责人** - 架构设计和核心开发
- **前端工程师** - UI/UX实现和交互优化
- **后端工程师** - API开发和数据处理
- **AI工程师** - 算法优化和模型训练

---

<div align="center">
  <p>🌟 如果这个项目对你有帮助，请给我们一个 Star！</p>
  <p>📧 联系我们：<EMAIL></p>
</div>
