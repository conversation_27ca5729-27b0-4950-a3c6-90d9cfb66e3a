import { Suspense } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import Layout from '../components/common/Layout';
import NotFoundPage from '../pages/NotFoundPage';
import LoadingComponent from '../components/common/LoadingComponent';
import { 
  StrategyPage, 
  SentimentPage, 
  ContentPage, 
  InfluencerPage,
  RoleTestPage 
} from './lazyRoutes';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Navigate to="/strategy" replace />
      },
      {
        path: 'strategy',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <StrategyPage />
          </Suspense>
        )
      },
      {
        path: 'sentiment',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <SentimentPage />
          </Suspense>
        )
      },
      {
        path: 'content',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <ContentPage />
          </Suspense>
        )
      },
      {
        path: 'influencer',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <InfluencerPage />
          </Suspense>
        )
      },
      {
        path: 'role-test',
        element: (
          <Suspense fallback={<LoadingComponent />}>
            <RoleTestPage />
          </Suspense>
        )
      },
      {
        path: '*',
        element: <NotFoundPage />
      }
    ]
  }
]);

export default router;
